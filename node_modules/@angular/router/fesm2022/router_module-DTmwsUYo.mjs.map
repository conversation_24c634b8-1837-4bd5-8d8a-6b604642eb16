{"version": 3, "file": "router_module-DTmwsUYo.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/directives/router_link.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/directives/router_link_active.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/router_preloader.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/router_scroller.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/provide_router.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/src/router_module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {\n  Attribute,\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  HostAttributeToken,\n  HostBinding,\n  HostListener,\n  inject,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Renderer2,\n  ɵRuntimeError as RuntimeError,\n  signal,\n  SimpleChanges,\n  untracked,\n  ɵINTERNAL_APPLICATION_ERROR_HANDLER,\n} from '@angular/core';\nimport {Subject, Subscription} from 'rxjs';\nimport {RuntimeErrorCode} from '../errors';\nimport {Event, NavigationEnd} from '../events';\nimport {QueryParamsHandling} from '../models';\nimport {Router} from '../router';\nimport {ROUTER_CONFIGURATION} from '../router_config';\nimport {ActivatedRoute} from '../router_state';\nimport {Params} from '../shared';\nimport {isUrlTree, UrlTree} from '../url_tree';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * ### RouterLink compatible custom elements\n *\n * In order to make a custom element work with routerLink, the corresponding custom\n * element must implement the `href` attribute and must list `href` in the array of\n * the static property/getter `observedAttributes`.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n@Directive({\n  selector: '[routerLink]',\n  host: {\n    '[attr.href]': 'reactiveHref()',\n  },\n})\nexport class RouterLink implements OnChanges, OnDestroy {\n  /** @nodoc */\n  protected readonly reactiveHref = signal<string | null>(null);\n  /**\n   * Represents an `href` attribute value applied to a host element,\n   * when a host element is an `<a>`/`<area>` tag or a compatible custom element.\n   * For other tags, the value is `null`.\n   */\n  get href() {\n    return untracked(this.reactiveHref);\n  }\n  /** @deprecated */\n  set href(value: string | null) {\n    this.reactiveHref.set(value);\n  }\n\n  /**\n   * Represents the `target` attribute on a host element.\n   * This is only used when the host element is\n   * an `<a>`/`<area>` tag or a compatible custom element.\n   */\n  @HostBinding('attr.target') @Input() target?: string;\n\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParams}\n   * @see {@link Router#createUrlTree}\n   */\n  @Input() queryParams?: Params | null;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#fragment}\n   * @see {@link Router#createUrlTree}\n   */\n  @Input() fragment?: string;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#queryParamsHandling}\n   * @see {@link Router#createUrlTree}\n   */\n  @Input() queryParamsHandling?: QueryParamsHandling | null;\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#state}\n   * @see {@link Router#navigateByUrl}\n   */\n  @Input() state?: {[k: string]: any};\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#info}\n   * @see {@link Router#navigateByUrl}\n   */\n  @Input() info?: unknown;\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * Specify a value here when you do not want to use the default value\n   * for `routerLink`, which is the current activated route.\n   * Note that a value of `undefined` here will use the `routerLink` default.\n   * @see {@link UrlCreationOptions#relativeTo}\n   * @see {@link Router#createUrlTree}\n   */\n  @Input() relativeTo?: ActivatedRoute | null;\n\n  /** Whether a host element is an `<a>`/`<area>` tag or a compatible custom element. */\n  private isAnchorElement: boolean;\n\n  private subscription?: Subscription;\n\n  /** @internal */\n  onChanges = new Subject<RouterLink>();\n\n  private readonly applicationErrorHandler = inject(ɵINTERNAL_APPLICATION_ERROR_HANDLER);\n  private readonly options = inject(ROUTER_CONFIGURATION, {optional: true});\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    @Attribute('tabindex') private readonly tabIndexAttribute: string | null | undefined,\n    private readonly renderer: Renderer2,\n    private readonly el: ElementRef,\n    private locationStrategy?: LocationStrategy,\n  ) {\n    // Set the initial href value to whatever exists on the host element already\n    this.reactiveHref.set(inject(new HostAttributeToken('href'), {optional: true}));\n    const tagName = el.nativeElement.tagName?.toLowerCase();\n    this.isAnchorElement =\n      tagName === 'a' ||\n      tagName === 'area' ||\n      !!(\n        // Avoid breaking in an SSR context where customElements might not be defined.\n        (\n          typeof customElements === 'object' &&\n          // observedAttributes is an optional static property/getter on a custom element.\n          // The spec states that this must be an array of strings.\n          (\n            customElements.get(tagName) as {observedAttributes?: string[]} | undefined\n          )?.observedAttributes?.includes?.('href')\n        )\n      );\n\n    if (!this.isAnchorElement) {\n      this.subscribeToNavigationEventsIfNecessary();\n    } else {\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n\n  private subscribeToNavigationEventsIfNecessary() {\n    if (this.subscription !== undefined || !this.isAnchorElement) {\n      return;\n    }\n\n    // preserving fragment in router state\n    let createSubcription = this.preserveFragment;\n    // preserving or merging with query params in router state\n    const dependsOnRouterState = (handling?: QueryParamsHandling | null) =>\n      handling === 'merge' || handling === 'preserve';\n    createSubcription ||= dependsOnRouterState(this.queryParamsHandling);\n    createSubcription ||=\n      !this.queryParamsHandling && !dependsOnRouterState(this.options?.defaultQueryParamsHandling);\n    if (!createSubcription) {\n      return;\n    }\n\n    this.subscription = this.router.events.subscribe((s: Event) => {\n      if (s instanceof NavigationEnd) {\n        this.updateHref();\n      }\n    });\n  }\n\n  /**\n   * Passed to {@link Router#createUrlTree} as part of the\n   * `UrlCreationOptions`.\n   * @see {@link UrlCreationOptions#preserveFragment}\n   * @see {@link Router#createUrlTree}\n   */\n  @Input({transform: booleanAttribute}) preserveFragment: boolean = false;\n\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#skipLocationChange}\n   * @see {@link Router#navigateByUrl}\n   */\n  @Input({transform: booleanAttribute}) skipLocationChange: boolean = false;\n\n  /**\n   * Passed to {@link Router#navigateByUrl} as part of the\n   * `NavigationBehaviorOptions`.\n   * @see {@link NavigationBehaviorOptions#replaceUrl}\n   * @see {@link Router#navigateByUrl}\n   */\n  @Input({transform: booleanAttribute}) replaceUrl: boolean = false;\n\n  /**\n   * Modifies the tab index if there was not a tabindex attribute on the element during\n   * instantiation.\n   */\n  private setTabIndexIfNotOnNativeEl(newTabIndex: string | null) {\n    if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n      return;\n    }\n    this.applyAttributeValue('tabindex', newTabIndex);\n  }\n\n  /** @docs-private */\n  // TODO(atscott): Remove changes parameter in major version as a breaking change.\n  ngOnChanges(changes?: SimpleChanges): void {\n    if (\n      ngDevMode &&\n      isUrlTree(this.routerLinkInput) &&\n      (this.fragment !== undefined ||\n        this.queryParams ||\n        this.queryParamsHandling ||\n        this.preserveFragment ||\n        this.relativeTo)\n    ) {\n      throw new RuntimeError(\n        RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS,\n        'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.',\n      );\n    }\n    if (this.isAnchorElement) {\n      this.updateHref();\n      this.subscribeToNavigationEventsIfNecessary();\n    }\n    // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n    // to the RouterLinks it's tracking.\n    this.onChanges.next(this);\n  }\n\n  private routerLinkInput: readonly any[] | UrlTree | null = null;\n\n  /**\n   * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n   *   - **array**: commands to pass to {@link Router#createUrlTree}.\n   *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n   *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n   *     and other inputs that correspond to properties of `UrlCreationOptions`.\n   *   - **null|undefined**: effectively disables the `routerLink`\n   * @see {@link Router#createUrlTree}\n   */\n  @Input()\n  set routerLink(commandsOrUrlTree: readonly any[] | string | UrlTree | null | undefined) {\n    if (commandsOrUrlTree == null) {\n      this.routerLinkInput = null;\n      this.setTabIndexIfNotOnNativeEl(null);\n    } else {\n      if (isUrlTree(commandsOrUrlTree)) {\n        this.routerLinkInput = commandsOrUrlTree;\n      } else {\n        this.routerLinkInput = Array.isArray(commandsOrUrlTree)\n          ? commandsOrUrlTree\n          : [commandsOrUrlTree];\n      }\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n\n  /** @docs-private */\n  @HostListener('click', [\n    '$event.button',\n    '$event.ctrlKey',\n    '$event.shiftKey',\n    '$event.altKey',\n    '$event.metaKey',\n  ])\n  onClick(\n    button: number,\n    ctrlKey: boolean,\n    shiftKey: boolean,\n    altKey: boolean,\n    metaKey: boolean,\n  ): boolean {\n    const urlTree = this.urlTree;\n\n    if (urlTree === null) {\n      return true;\n    }\n\n    if (this.isAnchorElement) {\n      if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n        return true;\n      }\n\n      if (typeof this.target === 'string' && this.target != '_self') {\n        return true;\n      }\n    }\n\n    const extras = {\n      skipLocationChange: this.skipLocationChange,\n      replaceUrl: this.replaceUrl,\n      state: this.state,\n      info: this.info,\n    };\n    // navigateByUrl is mocked frequently in tests... Reduce breakages when adding `catch`\n    this.router.navigateByUrl(urlTree, extras)?.catch((e) => {\n      this.applicationErrorHandler(e);\n    });\n\n    // Return `false` for `<a>` elements to prevent default action\n    // and cancel the native behavior, since the navigation is handled\n    // by the Router.\n    return !this.isAnchorElement;\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): any {\n    this.subscription?.unsubscribe();\n  }\n\n  private updateHref(): void {\n    const urlTree = this.urlTree;\n    this.reactiveHref.set(\n      urlTree !== null && this.locationStrategy\n        ? (this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree)) ?? '')\n        : null,\n    );\n  }\n\n  private applyAttributeValue(attrName: string, attrValue: string | null) {\n    const renderer = this.renderer;\n    const nativeElement = this.el.nativeElement;\n    if (attrValue !== null) {\n      renderer.setAttribute(nativeElement, attrName, attrValue);\n    } else {\n      renderer.removeAttribute(nativeElement, attrName);\n    }\n  }\n\n  get urlTree(): UrlTree | null {\n    if (this.routerLinkInput === null) {\n      return null;\n    } else if (isUrlTree(this.routerLinkInput)) {\n      return this.routerLinkInput;\n    }\n    return this.router.createUrlTree(this.routerLinkInput, {\n      // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n      // Otherwise, we should use the value provided by the user in the input.\n      relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: this.preserveFragment,\n    });\n  }\n}\n\n/**\n * @description\n * An alias for the `RouterLink` directive.\n * Deprecated since v15, use `RouterLink` directive instead.\n *\nexport { RouterLink as RouterLinkWithHref };\nnstead.\n * @publicApi\n */\nexport {RouterLink as RouterLinkWithHref};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  ChangeDetectorRef,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Output,\n  QueryList,\n  Renderer2,\n  SimpleChanges,\n} from '@angular/core';\nimport {from, of, Subscription} from 'rxjs';\nimport {mergeAll} from 'rxjs/operators';\n\nimport {Event, NavigationEnd} from '../events';\nimport {Router} from '../router';\nimport {IsActiveMatchOptions} from '../url_tree';\n\nimport {RouterLink} from './router_link';\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\n@Directive({\n  selector: '[routerLinkActive]',\n  exportAs: 'routerLinkActive',\n})\nexport class RouterLinkActive implements OnChanges, OnDestroy, AfterContentInit {\n  @ContentChildren(RouterLink, {descendants: true}) links!: QueryList<RouterLink>;\n\n  private classes: string[] = [];\n  private routerEventsSubscription: Subscription;\n  private linkInputChangesSubscription?: Subscription;\n  private _isActive = false;\n\n  get isActive(): boolean {\n    return this._isActive;\n  }\n\n  /**\n   * Options to configure how to determine if the router link is active.\n   *\n   * These options are passed to the `Router.isActive()` function.\n   *\n   * @see {@link Router#isActive}\n   */\n  @Input() routerLinkActiveOptions: {exact: boolean} | IsActiveMatchOptions = {exact: false};\n\n  /**\n   * Aria-current attribute to apply when the router link is active.\n   *\n   * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n   */\n  @Input() ariaCurrentWhenActive?: 'page' | 'step' | 'location' | 'date' | 'time' | true | false;\n\n  /**\n   *\n   * You can use the output `isActiveChange` to get notified each time the link becomes\n   * active or inactive.\n   *\n   * Emits:\n   * true  -> Route is active\n   * false -> Route is inactive\n   *\n   * ```html\n   * <a\n   *  routerLink=\"/user/bob\"\n   *  routerLinkActive=\"active-link\"\n   *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n   * ```\n   */\n  @Output() readonly isActiveChange: EventEmitter<boolean> = new EventEmitter();\n\n  constructor(\n    private router: Router,\n    private element: ElementRef,\n    private renderer: Renderer2,\n    private readonly cdr: ChangeDetectorRef,\n    @Optional() private link?: RouterLink,\n  ) {\n    this.routerEventsSubscription = router.events.subscribe((s: Event) => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n\n  /** @docs-private */\n  ngAfterContentInit(): void {\n    // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n    of(this.links.changes, of(null))\n      .pipe(mergeAll())\n      .subscribe((_) => {\n        this.update();\n        this.subscribeToEachLinkOnChanges();\n      });\n  }\n\n  private subscribeToEachLinkOnChanges() {\n    this.linkInputChangesSubscription?.unsubscribe();\n    const allLinkChanges = [...this.links.toArray(), this.link]\n      .filter((link): link is RouterLink => !!link)\n      .map((link) => link.onChanges);\n    this.linkInputChangesSubscription = from(allLinkChanges)\n      .pipe(mergeAll())\n      .subscribe((link) => {\n        if (this._isActive !== this.isLinkActive(this.router)(link)) {\n          this.update();\n        }\n      });\n  }\n\n  @Input()\n  set routerLinkActive(data: string[] | string) {\n    const classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter((c) => !!c);\n  }\n\n  /** @docs-private */\n  ngOnChanges(changes: SimpleChanges): void {\n    this.update();\n  }\n  /** @docs-private */\n  ngOnDestroy(): void {\n    this.routerEventsSubscription.unsubscribe();\n    this.linkInputChangesSubscription?.unsubscribe();\n  }\n\n  private update(): void {\n    if (!this.links || !this.router.navigated) return;\n\n    queueMicrotask(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      this.classes.forEach((c) => {\n        if (hasActiveLinks) {\n          this.renderer.addClass(this.element.nativeElement, c);\n        } else {\n          this.renderer.removeClass(this.element.nativeElement, c);\n        }\n      });\n      if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n        this.renderer.setAttribute(\n          this.element.nativeElement,\n          'aria-current',\n          this.ariaCurrentWhenActive.toString(),\n        );\n      } else {\n        this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n      }\n\n      // Only emit change if the active state changed.\n      if (this._isActive !== hasActiveLinks) {\n        this._isActive = hasActiveLinks;\n        this.cdr.markForCheck();\n        // Emit on isActiveChange after classes are updated\n        this.isActiveChange.emit(hasActiveLinks);\n      }\n    });\n  }\n\n  private isLinkActive(router: Router): (link: RouterLink) => boolean {\n    const options: boolean | IsActiveMatchOptions = isActiveMatchOptions(\n      this.routerLinkActiveOptions,\n    )\n      ? this.routerLinkActiveOptions\n      : // While the types should disallow `undefined` here, it's possible without strict inputs\n        this.routerLinkActiveOptions.exact || false;\n    return (link: RouterLink) => {\n      const urlTree = link.urlTree;\n      return urlTree ? router.isActive(urlTree, options) : false;\n    };\n  }\n\n  private hasActiveLinks(): boolean {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return (this.link && isActiveCheckFn(this.link)) || this.links.some(isActiveCheckFn);\n  }\n}\n\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(\n  options: {exact: boolean} | IsActiveMatchOptions,\n): options is IsActiveMatchOptions {\n  return !!(options as IsActiveMatchOptions).paths;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {createEnvironmentInjector, EnvironmentInjector, Injectable, OnDestroy} from '@angular/core';\nimport {from, Observable, of, Subscription} from 'rxjs';\nimport {catchError, concatMap, filter, mergeAll, mergeMap} from 'rxjs/operators';\n\nimport {Event, NavigationEnd} from './events';\nimport {LoadedRouterConfig, Route, Routes} from './models';\nimport {Router} from './router';\nimport {RouterConfigLoader} from './router_config_loader';\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nexport abstract class PreloadingStrategy {\n  abstract preload(route: Route, fn: () => Observable<any>): Observable<any>;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class PreloadAllModules implements PreloadingStrategy {\n  preload(route: Route, fn: () => Observable<any>): Observable<any> {\n    return fn().pipe(catchError(() => of(null)));\n  }\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class NoPreloading implements PreloadingStrategy {\n  preload(route: Route, fn: () => Observable<any>): Observable<any> {\n    return of(null);\n  }\n}\n\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class RouterPreloader implements OnDestroy {\n  private subscription?: Subscription;\n\n  constructor(\n    private router: Router,\n    private injector: EnvironmentInjector,\n    private preloadingStrategy: PreloadingStrategy,\n    private loader: RouterConfigLoader,\n  ) {}\n\n  setUpPreloading(): void {\n    this.subscription = this.router.events\n      .pipe(\n        filter((e: Event) => e instanceof NavigationEnd),\n        concatMap(() => this.preload()),\n      )\n      .subscribe(() => {});\n  }\n\n  preload(): Observable<any> {\n    return this.processRoutes(this.injector, this.router.config);\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n  private processRoutes(injector: EnvironmentInjector, routes: Routes): Observable<void> {\n    const res: Observable<any>[] = [];\n    for (const route of routes) {\n      if (route.providers && !route._injector) {\n        route._injector = createEnvironmentInjector(\n          route.providers,\n          injector,\n          `Route: ${route.path}`,\n        );\n      }\n\n      const injectorForCurrentRoute = route._injector ?? injector;\n      const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n\n      // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n      // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n      // happens as a consequence of needing to descend into children for route matching immediately\n      // while component loading is deferred until route activation. Because `canLoad` guards can\n      // have side effects, we cannot execute them here so we instead skip preloading altogether\n      // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n      // at all. Code splitting and lazy loading is separate from client-side authorization checks\n      // and should not be used as a security measure to prevent loading of code.\n      if (\n        (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined) ||\n        (route.loadComponent && !route._loadedComponent)\n      ) {\n        res.push(this.preloadConfig(injectorForCurrentRoute, route));\n      }\n      if (route.children || route._loadedRoutes) {\n        res.push(this.processRoutes(injectorForChildren, (route.children ?? route._loadedRoutes)!));\n      }\n    }\n    return from(res).pipe(mergeAll());\n  }\n\n  private preloadConfig(injector: EnvironmentInjector, route: Route): Observable<void> {\n    return this.preloadingStrategy.preload(route, () => {\n      let loadedChildren$: Observable<LoadedRouterConfig | null>;\n      if (route.loadChildren && route.canLoad === undefined) {\n        loadedChildren$ = this.loader.loadChildren(injector, route);\n      } else {\n        loadedChildren$ = of(null);\n      }\n\n      const recursiveLoadChildren$ = loadedChildren$.pipe(\n        mergeMap((config: LoadedRouterConfig | null) => {\n          if (config === null) {\n            return of(void 0);\n          }\n          route._loadedRoutes = config.routes;\n          route._loadedInjector = config.injector;\n          // If the loaded config was a module, use that as the module/module injector going\n          // forward. Otherwise, continue using the current module/module injector.\n          return this.processRoutes(config.injector ?? injector, config.routes);\n        }),\n      );\n      if (route.loadComponent && !route._loadedComponent) {\n        const loadComponent$ = this.loader.loadComponent(route);\n        return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n      } else {\n        return recursiveLoadChildren$;\n      }\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ViewportScroller} from '@angular/common';\nimport {Injectable, InjectionToken, NgZone, OnDestroy} from '@angular/core';\nimport {Unsubscribable} from 'rxjs';\n\nimport {\n  IMPERATIVE_NAVIGATION,\n  NavigationEnd,\n  NavigationSkipped,\n  NavigationSkippedCode,\n  NavigationStart,\n  NavigationTrigger,\n  Scroll,\n} from './events';\nimport {NavigationTransitions} from './navigation_transition';\nimport {UrlSerializer} from './url_tree';\n\nexport const ROUTER_SCROLLER = new InjectionToken<RouterScroller>('');\n\n@Injectable()\nexport class RouterScroller implements OnDestroy {\n  private routerEventsSubscription?: Unsubscribable;\n  private scrollEventsSubscription?: Unsubscribable;\n\n  private lastId = 0;\n  private lastSource: NavigationTrigger | undefined = IMPERATIVE_NAVIGATION;\n  private restoredId = 0;\n  private store: {[key: string]: [number, number]} = {};\n\n  /** @docs-private */\n  constructor(\n    readonly urlSerializer: UrlSerializer,\n    private transitions: NavigationTransitions,\n    public readonly viewportScroller: ViewportScroller,\n    private readonly zone: NgZone,\n    private options: {\n      scrollPositionRestoration?: 'disabled' | 'enabled' | 'top';\n      anchorScrolling?: 'disabled' | 'enabled';\n    } = {},\n  ) {\n    // Default both options to 'disabled'\n    options.scrollPositionRestoration ||= 'disabled';\n    options.anchorScrolling ||= 'disabled';\n  }\n\n  init(): void {\n    // we want to disable the automatic scrolling because having two places\n    // responsible for scrolling results race conditions, especially given\n    // that browser don't implement this behavior consistently\n    if (this.options.scrollPositionRestoration !== 'disabled') {\n      this.viewportScroller.setHistoryScrollRestoration('manual');\n    }\n    this.routerEventsSubscription = this.createScrollEvents();\n    this.scrollEventsSubscription = this.consumeScrollEvents();\n  }\n\n  private createScrollEvents() {\n    return this.transitions.events.subscribe((e) => {\n      if (e instanceof NavigationStart) {\n        // store the scroll position of the current stable navigations.\n        this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n        this.lastSource = e.navigationTrigger;\n        this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n      } else if (e instanceof NavigationEnd) {\n        this.lastId = e.id;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n      } else if (\n        e instanceof NavigationSkipped &&\n        e.code === NavigationSkippedCode.IgnoredSameUrlNavigation\n      ) {\n        this.lastSource = undefined;\n        this.restoredId = 0;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n      }\n    });\n  }\n\n  private consumeScrollEvents() {\n    return this.transitions.events.subscribe((e) => {\n      if (!(e instanceof Scroll)) return;\n      // a popstate event. The pop state event will always ignore anchor scrolling.\n      if (e.position) {\n        if (this.options.scrollPositionRestoration === 'top') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        } else if (this.options.scrollPositionRestoration === 'enabled') {\n          this.viewportScroller.scrollToPosition(e.position);\n        }\n        // imperative navigation \"forward\"\n      } else {\n        if (e.anchor && this.options.anchorScrolling === 'enabled') {\n          this.viewportScroller.scrollToAnchor(e.anchor);\n        } else if (this.options.scrollPositionRestoration !== 'disabled') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        }\n      }\n    });\n  }\n\n  private scheduleScrollEvent(\n    routerEvent: NavigationEnd | NavigationSkipped,\n    anchor: string | null,\n  ): void {\n    this.zone.runOutsideAngular(async () => {\n      // The scroll event needs to be delayed until after change detection. Otherwise, we may\n      // attempt to restore the scroll position before the router outlet has fully rendered the\n      // component by executing its update block of the template function.\n      //\n      // #57109 (we need to wait at least a macrotask before scrolling. AfterNextRender resolves in microtask event loop with Zones)\n      // We could consider _also_ waiting for a render promise though one should have already happened or been scheduled by this point\n      // and should definitely happen before rAF/setTimeout.\n      // #53985 (cannot rely solely on setTimeout because a frame may paint before the timeout)\n      await new Promise((resolve) => {\n        setTimeout(resolve);\n        if (typeof requestAnimationFrame !== 'undefined') {\n          requestAnimationFrame(resolve);\n        }\n      });\n      this.zone.run(() => {\n        this.transitions.events.next(\n          new Scroll(\n            routerEvent,\n            this.lastSource === 'popstate' ? this.store[this.restoredId] : null,\n            anchor,\n          ),\n        );\n      });\n    });\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    this.routerEventsSubscription?.unsubscribe();\n    this.scrollEventsSubscription?.unsubscribe();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  HashLocationStrategy,\n  LOCATION_INITIALIZED,\n  LocationStrategy,\n  ViewportScroller,\n} from '@angular/common';\nimport {\n  APP_BOOTSTRAP_LISTENER,\n  ApplicationRef,\n  ComponentRef,\n  ENVIRONMENT_INITIALIZER,\n  EnvironmentProviders,\n  inject,\n  InjectionToken,\n  Injector,\n  makeEnvironmentProviders,\n  NgZone,\n  provideAppInitializer,\n  Provider,\n  runInInjectionContext,\n  Type,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n} from '@angular/core';\nimport {of, Subject} from 'rxjs';\n\nimport {INPUT_BINDER, RoutedComponentInputBinder} from './directives/router_outlet';\nimport {Event, NavigationError, stringifyEvent} from './events';\nimport {RedirectCommand, Routes} from './models';\nimport {NAVIGATION_ERROR_HANDLER, NavigationTransitions} from './navigation_transition';\nimport {Router} from './router';\nimport {InMemoryScrollingOptions, ROUTER_CONFIGURATION, RouterConfigOptions} from './router_config';\nimport {ROUTES} from './router_config_loader';\nimport {PreloadingStrategy, RouterPreloader} from './router_preloader';\nimport {ROUTER_SCROLLER, RouterScroller} from './router_scroller';\nimport {ActivatedRoute} from './router_state';\nimport {UrlSerializer} from './url_tree';\nimport {afterNextNavigation} from './utils/navigations';\nimport {\n  CREATE_VIEW_TRANSITION,\n  createViewTransition,\n  VIEW_TRANSITION_OPTIONS,\n  ViewTransitionsFeatureOptions,\n} from './utils/view_transition';\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nexport function provideRouter(routes: Routes, ...features: RouterFeatures[]): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    {provide: ROUTES, multi: true, useValue: routes},\n    typeof ngDevMode === 'undefined' || ngDevMode\n      ? {provide: ROUTER_IS_PROVIDED, useValue: true}\n      : [],\n    {provide: ActivatedRoute, useFactory: rootRoute, deps: [Router]},\n    {provide: APP_BOOTSTRAP_LISTENER, multi: true, useFactory: getBootstrapListener},\n    features.map((feature) => feature.ɵproviders),\n  ]);\n}\n\nexport function rootRoute(router: Router): ActivatedRoute {\n  return router.routerState.root;\n}\n\n/**\n * Helper type to represent a Router feature.\n *\n * @publicApi\n */\nexport interface RouterFeature<FeatureKind extends RouterFeatureKind> {\n  ɵkind: FeatureKind;\n  ɵproviders: Array<Provider | EnvironmentProviders>;\n}\n\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature<FeatureKind extends RouterFeatureKind>(\n  kind: FeatureKind,\n  providers: Array<Provider | EnvironmentProviders>,\n): RouterFeature<FeatureKind> {\n  return {ɵkind: kind, ɵproviders: providers};\n}\n\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nexport const ROUTER_IS_PROVIDED = new InjectionToken<boolean>('', {\n  providedIn: 'root',\n  factory: () => false,\n});\n\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn(\n          '`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' +\n            'This is likely a mistake.',\n        );\n      }\n    };\n  },\n};\n\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nexport function provideRoutes(routes: Routes): Provider[] {\n  return [\n    {provide: ROUTES, multi: true, useValue: routes},\n    typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : [],\n  ];\n}\n\n/**\n * A type alias for providers returned by `withInMemoryScrolling` for use with `provideRouter`.\n *\n * @see {@link withInMemoryScrolling}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type InMemoryScrollingFeature = RouterFeature<RouterFeatureKind.InMemoryScrollingFeature>;\n\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nexport function withInMemoryScrolling(\n  options: InMemoryScrollingOptions = {},\n): InMemoryScrollingFeature {\n  const providers = [\n    {\n      provide: ROUTER_SCROLLER,\n      useFactory: () => {\n        const viewportScroller = inject(ViewportScroller);\n        const zone = inject(NgZone);\n        const transitions = inject(NavigationTransitions);\n        const urlSerializer = inject(UrlSerializer);\n        return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n      },\n    },\n  ];\n  return routerFeature(RouterFeatureKind.InMemoryScrollingFeature, providers);\n}\n\nexport function getBootstrapListener() {\n  const injector = inject(Injector);\n  return (bootstrappedComponentRef: ComponentRef<unknown>) => {\n    const ref = injector.get(ApplicationRef);\n\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n\n    if (injector.get(INITIAL_NAVIGATION) === InitialNavigation.EnabledNonBlocking) {\n      router.initialNavigation();\n    }\n\n    injector.get(ROUTER_PRELOADER, null, {optional: true})?.setUpPreloading();\n    injector.get(ROUTER_SCROLLER, null, {optional: true})?.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken<Subject<void>>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '',\n  {\n    factory: () => {\n      return new Subject<void>();\n    },\n  },\n);\n\n/**\n * This and the INITIAL_NAVIGATION token are used internally only. The public API side of this is\n * configured through the `ExtraOptions`.\n *\n * When set to `EnabledBlocking`, the initial navigation starts before the root\n * component is created. The bootstrap is blocked until the initial navigation is complete. This\n * value should be set in case you use [server-side rendering](guide/ssr), but do not enable\n * [hydration](guide/hydration) for your application.\n *\n * When set to `EnabledNonBlocking`, the initial navigation starts after the root component has been\n * created. The bootstrap is not blocked on the completion of the initial navigation.\n *\n * When set to `Disabled`, the initial navigation is not performed. The location listener is set up\n * before the root component gets created. Use if there is a reason to have more control over when\n * the router starts its initial navigation due to some complex initialization logic.\n *\n * @see {@link ExtraOptions}\n */\nconst enum InitialNavigation {\n  EnabledBlocking,\n  EnabledNonBlocking,\n  Disabled,\n}\n\nconst INITIAL_NAVIGATION = new InjectionToken<InitialNavigation>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '',\n  {providedIn: 'root', factory: () => InitialNavigation.EnabledNonBlocking},\n);\n\n/**\n * A type alias for providers returned by `withEnabledBlockingInitialNavigation` for use with\n * `provideRouter`.\n *\n * @see {@link withEnabledBlockingInitialNavigation}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type EnabledBlockingInitialNavigationFeature =\n  RouterFeature<RouterFeatureKind.EnabledBlockingInitialNavigationFeature>;\n\n/**\n * A type alias for providers returned by `withEnabledBlockingInitialNavigation` or\n * `withDisabledInitialNavigation` functions for use with `provideRouter`.\n *\n * @see {@link withEnabledBlockingInitialNavigation}\n * @see {@link withDisabledInitialNavigation}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type InitialNavigationFeature =\n  | EnabledBlockingInitialNavigationFeature\n  | DisabledInitialNavigationFeature;\n\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nexport function withEnabledBlockingInitialNavigation(): EnabledBlockingInitialNavigationFeature {\n  const providers = [\n    {provide: INITIAL_NAVIGATION, useValue: InitialNavigation.EnabledBlocking},\n    provideAppInitializer(() => {\n      const injector = inject(Injector);\n      const locationInitialized: Promise<any> = injector.get(\n        LOCATION_INITIALIZED,\n        Promise.resolve(),\n      );\n\n      return locationInitialized.then(() => {\n        return new Promise((resolve) => {\n          const router = injector.get(Router);\n          const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n          afterNextNavigation(router, () => {\n            // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n            // without a redirect.\n            resolve(true);\n          });\n\n          injector.get(NavigationTransitions).afterPreactivation = () => {\n            // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n            // assume activation will complete successfully (even though this is not\n            // guaranteed).\n            resolve(true);\n            return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n          };\n          router.initialNavigation();\n        });\n      });\n    }),\n  ];\n  return routerFeature(RouterFeatureKind.EnabledBlockingInitialNavigationFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withDisabledInitialNavigation` for use with\n * `provideRouter`.\n *\n * @see {@link withDisabledInitialNavigation}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type DisabledInitialNavigationFeature =\n  RouterFeature<RouterFeatureKind.DisabledInitialNavigationFeature>;\n\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withDisabledInitialNavigation(): DisabledInitialNavigationFeature {\n  const providers = [\n    provideAppInitializer(() => {\n      inject(Router).setUpLocationChangeListener();\n    }),\n    {provide: INITIAL_NAVIGATION, useValue: InitialNavigation.Disabled},\n  ];\n  return routerFeature(RouterFeatureKind.DisabledInitialNavigationFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withDebugTracing` for use with `provideRouter`.\n *\n * @see {@link withDebugTracing}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type DebugTracingFeature = RouterFeature<RouterFeatureKind.DebugTracingFeature>;\n\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withDebugTracing(): DebugTracingFeature {\n  let providers: Provider[] = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [\n      {\n        provide: ENVIRONMENT_INITIALIZER,\n        multi: true,\n        useFactory: () => {\n          const router = inject(Router);\n          return () =>\n            router.events.subscribe((e: Event) => {\n              // tslint:disable:no-console\n              console.group?.(`Router Event: ${(<any>e.constructor).name}`);\n              console.log(stringifyEvent(e));\n              console.log(e);\n              console.groupEnd?.();\n              // tslint:enable:no-console\n            });\n        },\n      },\n    ];\n  } else {\n    providers = [];\n  }\n  return routerFeature(RouterFeatureKind.DebugTracingFeature, providers);\n}\n\nconst ROUTER_PRELOADER = new InjectionToken<RouterPreloader>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '',\n);\n\n/**\n * A type alias that represents a feature which enables preloading in Router.\n * The type is used to describe the return value of the `withPreloading` function.\n *\n * @see {@link withPreloading}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type PreloadingFeature = RouterFeature<RouterFeatureKind.PreloadingFeature>;\n\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withPreloading(preloadingStrategy: Type<PreloadingStrategy>): PreloadingFeature {\n  const providers = [\n    {provide: ROUTER_PRELOADER, useExisting: RouterPreloader},\n    {provide: PreloadingStrategy, useExisting: preloadingStrategy},\n  ];\n  return routerFeature(RouterFeatureKind.PreloadingFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withRouterConfig` for use with `provideRouter`.\n *\n * @see {@link withRouterConfig}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type RouterConfigurationFeature =\n  RouterFeature<RouterFeatureKind.RouterConfigurationFeature>;\n\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withRouterConfig(options: RouterConfigOptions): RouterConfigurationFeature {\n  const providers = [{provide: ROUTER_CONFIGURATION, useValue: options}];\n  return routerFeature(RouterFeatureKind.RouterConfigurationFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withHashLocation` for use with `provideRouter`.\n *\n * @see {@link withHashLocation}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type RouterHashLocationFeature = RouterFeature<RouterFeatureKind.RouterHashLocationFeature>;\n\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withHashLocation(): RouterHashLocationFeature {\n  const providers = [{provide: LocationStrategy, useClass: HashLocationStrategy}];\n  return routerFeature(RouterFeatureKind.RouterHashLocationFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withNavigationErrorHandler` for use with `provideRouter`.\n *\n * @see {@link withNavigationErrorHandler}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type NavigationErrorHandlerFeature =\n  RouterFeature<RouterFeatureKind.NavigationErrorHandlerFeature>;\n\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nexport function withNavigationErrorHandler(\n  handler: (error: NavigationError) => unknown | RedirectCommand,\n): NavigationErrorHandlerFeature {\n  const providers = [\n    {\n      provide: NAVIGATION_ERROR_HANDLER,\n      useValue: handler,\n    },\n  ];\n  return routerFeature(RouterFeatureKind.NavigationErrorHandlerFeature, providers);\n}\n\n/**\n * A type alias for providers returned by `withComponentInputBinding` for use with `provideRouter`.\n *\n * @see {@link withComponentInputBinding}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type ComponentInputBindingFeature =\n  RouterFeature<RouterFeatureKind.ComponentInputBindingFeature>;\n\n/**\n * A type alias for providers returned by `withViewTransitions` for use with `provideRouter`.\n *\n * @see {@link withViewTransitions}\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type ViewTransitionsFeature = RouterFeature<RouterFeatureKind.ViewTransitionsFeature>;\n\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nexport function withComponentInputBinding(): ComponentInputBindingFeature {\n  const providers = [\n    RoutedComponentInputBinder,\n    {provide: INPUT_BINDER, useExisting: RoutedComponentInputBinder},\n  ];\n\n  return routerFeature(RouterFeatureKind.ComponentInputBindingFeature, providers);\n}\n\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview 19.0\n */\nexport function withViewTransitions(\n  options?: ViewTransitionsFeatureOptions,\n): ViewTransitionsFeature {\n  performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [\n    {provide: CREATE_VIEW_TRANSITION, useValue: createViewTransition},\n    {\n      provide: VIEW_TRANSITION_OPTIONS,\n      useValue: {skipNextTransition: !!options?.skipInitialTransition, ...options},\n    },\n  ];\n  return routerFeature(RouterFeatureKind.ViewTransitionsFeature, providers);\n}\n\n/**\n * A type alias that represents all Router features available for use with `provideRouter`.\n * Features can be enabled by adding special functions to the `provideRouter` call.\n * See documentation for each symbol to find corresponding function name. See also `provideRouter`\n * documentation on how to use those functions.\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n */\nexport type RouterFeatures =\n  | PreloadingFeature\n  | DebugTracingFeature\n  | InitialNavigationFeature\n  | InMemoryScrollingFeature\n  | RouterConfigurationFeature\n  | NavigationErrorHandlerFeature\n  | ComponentInputBindingFeature\n  | ViewTransitionsFeature\n  | RouterHashLocationFeature;\n\n/**\n * The list of features as an enum to uniquely type each feature.\n */\nexport const enum RouterFeatureKind {\n  PreloadingFeature,\n  DebugTracingFeature,\n  EnabledBlockingInitialNavigationFeature,\n  DisabledInitialNavigationFeature,\n  InMemoryScrollingFeature,\n  RouterConfigurationFeature,\n  RouterHashLocationFeature,\n  NavigationErrorHandlerFeature,\n  ComponentInputBindingFeature,\n  ViewTransitionsFeature,\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  HashLocationStrategy,\n  Location,\n  LocationStrategy,\n  PathLocationStrategy,\n  ViewportScroller,\n} from '@angular/common';\nimport {\n  APP_BOOTSTRAP_LISTENER,\n  ComponentRef,\n  inject,\n  Inject,\n  InjectionToken,\n  ModuleWithProviders,\n  NgModule,\n  NgZone,\n  Optional,\n  Provider,\n  SkipSelf,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {EmptyOutletComponent} from './components/empty_outlet';\nimport {RouterLink} from './directives/router_link';\nimport {RouterLinkActive} from './directives/router_link_active';\nimport {RouterOutlet} from './directives/router_outlet';\nimport {RuntimeErrorCode} from './errors';\nimport {Routes} from './models';\nimport {NAVIGATION_ERROR_HANDLER, NavigationTransitions} from './navigation_transition';\nimport {\n  getBootstrapListener,\n  rootRoute,\n  ROUTER_IS_PROVIDED,\n  withComponentInputBinding,\n  withDebugTracing,\n  withDisabledInitialNavigation,\n  withEnabledBlockingInitialNavigation,\n  withPreloading,\n  withViewTransitions,\n} from './provide_router';\nimport {Router} from './router';\nimport {ExtraOptions, ROUTER_CONFIGURATION} from './router_config';\nimport {RouterConfigLoader, ROUTES} from './router_config_loader';\nimport {ChildrenOutletContexts} from './router_outlet_context';\nimport {ROUTER_SCROLLER, RouterScroller} from './router_scroller';\nimport {ActivatedRoute} from './router_state';\nimport {DefaultUrlSerializer, UrlSerializer} from './url_tree';\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, EmptyOutletComponent];\n\n/**\n * @docsNotRequired\n */\nexport const ROUTER_FORROOT_GUARD = new InjectionToken<void>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '',\n);\n\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nexport const ROUTER_PROVIDERS: Provider[] = [\n  Location,\n  {provide: UrlSerializer, useClass: DefaultUrlSerializer},\n  Router,\n  ChildrenOutletContexts,\n  {provide: ActivatedRoute, useFactory: rootRoute, deps: [Router]},\n  RouterConfigLoader,\n  // Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n  // be removed when `provideRoutes` is removed.\n  typeof ngDevMode === 'undefined' || ngDevMode\n    ? {provide: ROUTER_IS_PROVIDED, useValue: true}\n    : [],\n];\n\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\n@NgModule({\n  imports: ROUTER_DIRECTIVES,\n  exports: ROUTER_DIRECTIVES,\n})\nexport class RouterModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      inject(ROUTER_FORROOT_GUARD, {optional: true});\n    }\n  }\n\n  /**\n   * Creates and configures a module with all the router providers and directives.\n   * Optionally sets up an application listener to perform an initial navigation.\n   *\n   * When registering the NgModule at the root, import as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forRoot(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the application.\n   * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n   * @return The new `NgModule`.\n   *\n   */\n  static forRoot(routes: Routes, config?: ExtraOptions): ModuleWithProviders<RouterModule> {\n    return {\n      ngModule: RouterModule,\n      providers: [\n        ROUTER_PROVIDERS,\n        typeof ngDevMode === 'undefined' || ngDevMode\n          ? config?.enableTracing\n            ? withDebugTracing().ɵproviders\n            : []\n          : [],\n        {provide: ROUTES, multi: true, useValue: routes},\n        typeof ngDevMode === 'undefined' || ngDevMode\n          ? {\n              provide: ROUTER_FORROOT_GUARD,\n              useFactory: provideForRootGuard,\n              deps: [[Router, new Optional(), new SkipSelf()]],\n            }\n          : [],\n        config?.errorHandler\n          ? {\n              provide: NAVIGATION_ERROR_HANDLER,\n              useValue: config.errorHandler,\n            }\n          : [],\n        {provide: ROUTER_CONFIGURATION, useValue: config ? config : {}},\n        config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(),\n        provideRouterScroller(),\n        config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [],\n        config?.initialNavigation ? provideInitialNavigation(config) : [],\n        config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [],\n        config?.enableViewTransitions ? withViewTransitions().ɵproviders : [],\n        provideRouterInitializer(),\n      ],\n    };\n  }\n\n  /**\n   * Creates a module with all the router directives and a provider registering routes,\n   * without creating a new Router service.\n   * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forChild(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n   * @return The new NgModule.\n   *\n   */\n  static forChild(routes: Routes): ModuleWithProviders<RouterModule> {\n    return {\n      ngModule: RouterModule,\n      providers: [{provide: ROUTES, multi: true, useValue: routes}],\n    };\n  }\n}\n\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nexport function provideRouterScroller(): Provider {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config: ExtraOptions = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    },\n  };\n}\n\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy(): Provider {\n  return {provide: LocationStrategy, useClass: HashLocationStrategy};\n}\n\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy(): Provider {\n  return {provide: LocationStrategy, useClass: PathLocationStrategy};\n}\n\nexport function provideForRootGuard(router: Router): any {\n  if (router) {\n    throw new RuntimeError(\n      RuntimeErrorCode.FOR_ROOT_CALLED_TWICE,\n      `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` +\n        ` Lazy loaded modules should use RouterModule.forChild() instead.`,\n    );\n  }\n  return 'guarded';\n}\n\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config: Pick<ExtraOptions, 'initialNavigation'>): Provider[] {\n  return [\n    config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [],\n    config.initialNavigation === 'enabledBlocking'\n      ? withEnabledBlockingInitialNavigation().ɵproviders\n      : [],\n  ];\n}\n\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nexport const ROUTER_INITIALIZER = new InjectionToken<(compRef: ComponentRef<any>) => void>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '',\n);\n\nfunction provideRouterInitializer(): Provider[] {\n  return [\n    // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n    // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n    {provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener},\n    {provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER},\n  ];\n}\n"], "names": ["ɵINTERNAL_APPLICATION_ERROR_HANDLER", "RuntimeError", "i1.Router", "i2.RouterLink", "i2.RouterConfigLoader", "performanceMarkFeature", "EmptyOutletComponent"], "mappings": ";;;;;;;;;;;;;;AAsCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGG;MAOU,UAAU,CAAA;AAiFX,IAAA,MAAA;AACA,IAAA,KAAA;AACgC,IAAA,iBAAA;AACvB,IAAA,QAAA;AACA,IAAA,EAAA;AACT,IAAA,gBAAA;;AApFS,IAAA,YAAY,GAAG,MAAM,CAAgB,IAAI,CAAC;AAC7D;;;;AAIG;AACH,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;;;IAGrC,IAAI,IAAI,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;;AAG9B;;;;AAIG;AACkC,IAAA,MAAM;AAE3C;;;;;AAKG;AACM,IAAA,WAAW;AACpB;;;;;AAKG;AACM,IAAA,QAAQ;AACjB;;;;;AAKG;AACM,IAAA,mBAAmB;AAC5B;;;;;AAKG;AACM,IAAA,KAAK;AACd;;;;;AAKG;AACM,IAAA,IAAI;AACb;;;;;;;;AAQG;AACM,IAAA,UAAU;;AAGX,IAAA,eAAe;AAEf,IAAA,YAAY;;AAGpB,IAAA,SAAS,GAAG,IAAI,OAAO,EAAc;AAEpB,IAAA,uBAAuB,GAAG,MAAM,CAACA,mCAAmC,CAAC;IACrE,OAAO,GAAG,MAAM,CAAC,oBAAoB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEzE,WACU,CAAA,MAAc,EACd,KAAqB,EACW,iBAA4C,EACnE,QAAmB,EACnB,EAAc,EACvB,gBAAmC,EAAA;QALnC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAK,CAAA,KAAA,GAAL,KAAK;QAC2B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACxC,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAE,CAAA,EAAA,GAAF,EAAE;QACX,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;;QAGxB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC/E,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE;AACvD,QAAA,IAAI,CAAC,eAAe;AAClB,YAAA,OAAO,KAAK,GAAG;AACf,gBAAA,OAAO,KAAK,MAAM;AAClB,gBAAA,CAAC;;iBAGG,OAAO,cAAc,KAAK,QAAQ;;;AAIhC,oBAAA,cAAc,CAAC,GAAG,CAAC,OAAO,CAC3B,EAAE,kBAAkB,EAAE,QAAQ,GAAG,MAAM,CAAC,EAE5C;AAEH,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,sCAAsC,EAAE;;aACxC;AACL,YAAA,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC;;;IAIhC,sCAAsC,GAAA;QAC5C,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC5D;;;AAIF,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB;;AAE7C,QAAA,MAAM,oBAAoB,GAAG,CAAC,QAAqC,KACjE,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,UAAU;AACjD,QAAA,iBAAiB,KAAK,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACpE,iBAAiB;AACf,YAAA,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,0BAA0B,CAAC;QAC9F,IAAI,CAAC,iBAAiB,EAAE;YACtB;;AAGF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAQ,KAAI;AAC5D,YAAA,IAAI,CAAC,YAAY,aAAa,EAAE;gBAC9B,IAAI,CAAC,UAAU,EAAE;;AAErB,SAAC,CAAC;;AAGJ;;;;;AAKG;IACmC,gBAAgB,GAAY,KAAK;AAEvE;;;;;AAKG;IACmC,kBAAkB,GAAY,KAAK;AAEzE;;;;;AAKG;IACmC,UAAU,GAAY,KAAK;AAEjE;;;AAGG;AACK,IAAA,0BAA0B,CAAC,WAA0B,EAAA;AAC3D,QAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,sCAAsC,IAAI,CAAC,eAAe,EAAE;YAC5F;;AAEF,QAAA,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,WAAW,CAAC;;;;AAKnD,IAAA,WAAW,CAAC,OAAuB,EAAA;AACjC,QAAA,IACE,SAAS;AACT,YAAA,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC;AAC/B,aAAC,IAAI,CAAC,QAAQ,KAAK,SAAS;AAC1B,gBAAA,IAAI,CAAC,WAAW;AAChB,gBAAA,IAAI,CAAC,mBAAmB;AACxB,gBAAA,IAAI,CAAC,gBAAgB;AACrB,gBAAA,IAAI,CAAC,UAAU,CAAC,EAClB;AACA,YAAA,MAAM,IAAIC,aAAY,CAEpB,IAAA,oDAAA,8FAA8F,CAC/F;;AAEH,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,sCAAsC,EAAE;;;;AAI/C,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGnB,eAAe,GAAoC,IAAI;AAE/D;;;;;;;;AAQG;IACH,IACI,UAAU,CAAC,iBAAuE,EAAA;AACpF,QAAA,IAAI,iBAAiB,IAAI,IAAI,EAAE;AAC7B,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC;;aAChC;AACL,YAAA,IAAI,SAAS,CAAC,iBAAiB,CAAC,EAAE;AAChC,gBAAA,IAAI,CAAC,eAAe,GAAG,iBAAiB;;iBACnC;gBACL,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB;AACpD,sBAAE;AACF,sBAAE,CAAC,iBAAiB,CAAC;;AAEzB,YAAA,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC;;;;IAYxC,OAAO,CACL,MAAc,EACd,OAAgB,EAChB,QAAiB,EACjB,MAAe,EACf,OAAgB,EAAA;AAEhB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;AAE5B,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,IAAI,OAAO,EAAE;AAC5D,gBAAA,OAAO,IAAI;;AAGb,YAAA,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,EAAE;AAC7D,gBAAA,OAAO,IAAI;;;AAIf,QAAA,MAAM,MAAM,GAAG;YACb,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB;;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAI;AACtD,YAAA,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;AACjC,SAAC,CAAC;;;;AAKF,QAAA,OAAO,CAAC,IAAI,CAAC,eAAe;;;IAI9B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE;;IAG1B,UAAU,GAAA;AAChB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;QAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,CACnB,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC;AACvB,eAAG,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE;cACnF,IAAI,CACT;;IAGK,mBAAmB,CAAC,QAAgB,EAAE,SAAwB,EAAA;AACpE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa;AAC3C,QAAA,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;;aACpD;AACL,YAAA,QAAQ,CAAC,eAAe,CAAC,aAAa,EAAE,QAAQ,CAAC;;;AAIrD,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AACjC,YAAA,OAAO,IAAI;;AACN,aAAA,IAAI,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;YAC1C,OAAO,IAAI,CAAC,eAAe;;QAE7B,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE;;;AAGrD,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK;YACxE,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AACxC,SAAA,CAAC;;AAxTO,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,gEAmFR,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAnFZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,UAAU,EA+IF,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,CAQhB,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,4CAQhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,oFAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,aAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGA/JxB,UAAU,EAAA,UAAA,EAAA,CAAA;kBANtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,IAAI,EAAE;AACJ,wBAAA,aAAa,EAAE,gBAAgB;AAChC,qBAAA;AACF,iBAAA;;0BAoFI,SAAS;2BAAC,UAAU;yHA9Dc,MAAM,EAAA,CAAA;sBAA1C,WAAW;uBAAC,aAAa;;sBAAG;gBAQpB,WAAW,EAAA,CAAA;sBAAnB;gBAOQ,QAAQ,EAAA,CAAA;sBAAhB;gBAOQ,mBAAmB,EAAA,CAAA;sBAA3B;gBAOQ,KAAK,EAAA,CAAA;sBAAb;gBAOQ,IAAI,EAAA,CAAA;sBAAZ;gBAUQ,UAAU,EAAA,CAAA;sBAAlB;gBA4EqC,gBAAgB,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAQE,kBAAkB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAQE,UAAU,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAmDhC,UAAU,EAAA,CAAA;sBADb;gBAyBD,OAAO,EAAA,CAAA;sBAPN,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE;wBACrB,eAAe;wBACf,gBAAgB;wBAChB,iBAAiB;wBACjB,eAAe;wBACf,gBAAgB;AACjB,qBAAA;;;AC9VH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEG;MAKU,gBAAgB,CAAA;AAiDjB,IAAA,MAAA;AACA,IAAA,OAAA;AACA,IAAA,QAAA;AACS,IAAA,GAAA;AACG,IAAA,IAAA;AApD4B,IAAA,KAAK;IAE/C,OAAO,GAAa,EAAE;AACtB,IAAA,wBAAwB;AACxB,IAAA,4BAA4B;IAC5B,SAAS,GAAG,KAAK;AAEzB,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;AAGvB;;;;;;AAMG;AACM,IAAA,uBAAuB,GAA4C,EAAC,KAAK,EAAE,KAAK,EAAC;AAE1F;;;;;;AAMG;AACM,IAAA,qBAAqB;AAE9B;;;;;;;;;;;;;;;AAeG;AACgB,IAAA,cAAc,GAA0B,IAAI,YAAY,EAAE;IAE7E,WACU,CAAA,MAAc,EACd,OAAmB,EACnB,QAAmB,EACV,GAAsB,EACnB,IAAiB,EAAA;QAJ7B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACC,IAAG,CAAA,GAAA,GAAH,GAAG;QACA,IAAI,CAAA,IAAA,GAAJ,IAAI;AAExB,QAAA,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAQ,KAAI;AACnE,YAAA,IAAI,CAAC,YAAY,aAAa,EAAE;gBAC9B,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC,CAAC;;;IAIJ,kBAAkB,GAAA;;QAEhB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC;aAC5B,IAAI,CAAC,QAAQ,EAAE;AACf,aAAA,SAAS,CAAC,CAAC,CAAC,KAAI;YACf,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,4BAA4B,EAAE;AACrC,SAAC,CAAC;;IAGE,4BAA4B,GAAA;AAClC,QAAA,IAAI,CAAC,4BAA4B,EAAE,WAAW,EAAE;AAChD,QAAA,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI;aACvD,MAAM,CAAC,CAAC,IAAI,KAAyB,CAAC,CAAC,IAAI;aAC3C,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC;AAChC,QAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,cAAc;aACpD,IAAI,CAAC,QAAQ,EAAE;AACf,aAAA,SAAS,CAAC,CAAC,IAAI,KAAI;AAClB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;gBAC3D,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC,CAAC;;IAGN,IACI,gBAAgB,CAAC,IAAuB,EAAA;QAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AAC5D,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;AAI3C,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,CAAC,MAAM,EAAE;;;IAGf,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;AAC3C,QAAA,IAAI,CAAC,4BAA4B,EAAE,WAAW,EAAE;;IAG1C,MAAM,GAAA;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS;YAAE;QAE3C,cAAc,CAAC,MAAK;AAClB,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;gBACzB,IAAI,cAAc,EAAE;AAClB,oBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;;qBAChD;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;;AAE5D,aAAC,CAAC;YACF,IAAI,cAAc,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;gBAC9D,IAAI,CAAC,QAAQ,CAAC,YAAY,CACxB,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,cAAc,EACd,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CACtC;;iBACI;AACL,gBAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC;;;AAI3E,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,cAAc,EAAE;AACrC,gBAAA,IAAI,CAAC,SAAS,GAAG,cAAc;AAC/B,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAEvB,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;;AAE5C,SAAC,CAAC;;AAGI,IAAA,YAAY,CAAC,MAAc,EAAA;AACjC,QAAA,MAAM,OAAO,GAAmC,oBAAoB,CAClE,IAAI,CAAC,uBAAuB;cAE1B,IAAI,CAAC;AACP;AACE,gBAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,IAAI,KAAK;QAC/C,OAAO,CAAC,IAAgB,KAAI;AAC1B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,YAAA,OAAO,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK;AAC5D,SAAC;;IAGK,cAAc,GAAA;QACpB,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;QACtD,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC;;kHAtJ3E,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,gBAAgB,ySACV,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGADhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,kBAAkB;AAC7B,iBAAA;;0BAsDI;yCApD+C,KAAK,EAAA,CAAA;sBAAtD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAkBvC,uBAAuB,EAAA,CAAA;sBAA/B;gBASQ,qBAAqB,EAAA,CAAA;sBAA7B;gBAkBkB,cAAc,EAAA,CAAA;sBAAhC;gBA0CG,gBAAgB,EAAA,CAAA;sBADnB;;AAmEH;;AAEG;AACH,SAAS,oBAAoB,CAC3B,OAAgD,EAAA;AAEhD,IAAA,OAAO,CAAC,CAAE,OAAgC,CAAC,KAAK;AAClD;;AC1PA;;;;;;AAMG;MACmB,kBAAkB,CAAA;AAEvC;AAED;;;;;;;;;;AAUG;MAEU,iBAAiB,CAAA;IAC5B,OAAO,CAAC,KAAY,EAAE,EAAyB,EAAA;AAC7C,QAAA,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;kHAFnC,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA;;sGAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAOhC;;;;;;;;AAQG;MAEU,YAAY,CAAA;IACvB,OAAO,CAAC,KAAY,EAAE,EAAyB,EAAA;AAC7C,QAAA,OAAO,EAAE,CAAC,IAAI,CAAC;;kHAFN,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA;;sGAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAOhC;;;;;;;;;;;AAWG;MAEU,eAAe,CAAA;AAIhB,IAAA,MAAA;AACA,IAAA,QAAA;AACA,IAAA,kBAAA;AACA,IAAA,MAAA;AANF,IAAA,YAAY;AAEpB,IAAA,WAAA,CACU,MAAc,EACd,QAA6B,EAC7B,kBAAsC,EACtC,MAA0B,EAAA;QAH1B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB;QAClB,IAAM,CAAA,MAAA,GAAN,MAAM;;IAGhB,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;aAC7B,IAAI,CACH,MAAM,CAAC,CAAC,CAAQ,KAAK,CAAC,YAAY,aAAa,CAAC,EAChD,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AAEhC,aAAA,SAAS,CAAC,MAAO,GAAC,CAAC;;IAGxB,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;;;IAI9D,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;;;IAI3B,aAAa,CAAC,QAA6B,EAAE,MAAc,EAAA;QACjE,MAAM,GAAG,GAAsB,EAAE;AACjC,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;AACvC,gBAAA,KAAK,CAAC,SAAS,GAAG,yBAAyB,CACzC,KAAK,CAAC,SAAS,EACf,QAAQ,EACR,CAAU,OAAA,EAAA,KAAK,CAAC,IAAI,CAAA,CAAE,CACvB;;AAGH,YAAA,MAAM,uBAAuB,GAAG,KAAK,CAAC,SAAS,IAAI,QAAQ;AAC3D,YAAA,MAAM,mBAAmB,GAAG,KAAK,CAAC,eAAe,IAAI,uBAAuB;;;;;;;;;AAU5E,YAAA,IACE,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS;iBACzE,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAChD;AACA,gBAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;;YAE9D,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;gBACzC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAG,CAAC;;;QAG/F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;;IAG3B,aAAa,CAAC,QAA6B,EAAE,KAAY,EAAA;QAC/D,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,MAAK;AACjD,YAAA,IAAI,eAAsD;YAC1D,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;gBACrD,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC;;iBACtD;AACL,gBAAA,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC;;YAG5B,MAAM,sBAAsB,GAAG,eAAe,CAAC,IAAI,CACjD,QAAQ,CAAC,CAAC,MAAiC,KAAI;AAC7C,gBAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,oBAAA,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;;AAEnB,gBAAA,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM;AACnC,gBAAA,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,QAAQ;;;AAGvC,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC;aACtE,CAAC,CACH;YACD,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC;AACvD,gBAAA,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;;iBACjE;AACL,gBAAA,OAAO,sBAAsB;;AAEjC,SAAC,CAAC;;kHA5FO,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAD,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,kBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAf,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA;;sGAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;AClDzB,MAAM,eAAe,GAAG,IAAI,cAAc,CAAiB,EAAE,CAAC;MAGxD,cAAc,CAAA;AAWd,IAAA,aAAA;AACD,IAAA,WAAA;AACQ,IAAA,gBAAA;AACC,IAAA,IAAA;AACT,IAAA,OAAA;AAdF,IAAA,wBAAwB;AACxB,IAAA,wBAAwB;IAExB,MAAM,GAAG,CAAC;IACV,UAAU,GAAkC,qBAAqB;IACjE,UAAU,GAAG,CAAC;IACd,KAAK,GAAsC,EAAE;;IAGrD,WACW,CAAA,aAA4B,EAC7B,WAAkC,EAC1B,gBAAkC,EACjC,IAAY,EACrB,OAAA,GAGJ,EAAE,EAAA;QAPG,IAAa,CAAA,aAAA,GAAb,aAAa;QACd,IAAW,CAAA,WAAA,GAAX,WAAW;QACH,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QACf,IAAI,CAAA,IAAA,GAAJ,IAAI;QACb,IAAO,CAAA,OAAA,GAAP,OAAO;;AAMf,QAAA,OAAO,CAAC,yBAAyB,KAAK,UAAU;AAChD,QAAA,OAAO,CAAC,eAAe,KAAK,UAAU;;IAGxC,IAAI,GAAA;;;;QAIF,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,KAAK,UAAU,EAAE;AACzD,YAAA,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,QAAQ,CAAC;;AAE7D,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,EAAE;AACzD,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,mBAAmB,EAAE;;IAGpD,kBAAkB,GAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;AAC7C,YAAA,IAAI,CAAC,YAAY,eAAe,EAAE;;AAEhC,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE;AACnE,gBAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,iBAAiB;AACrC,gBAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC,YAAY,GAAG,CAAC;;AAC/D,iBAAA,IAAI,CAAC,YAAY,aAAa,EAAE;AACrC,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAClB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC;;iBAC9E,IACL,CAAC,YAAY,iBAAiB;AAC9B,gBAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,CAAC,wBAAwB,EACzD;AACA,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,gBAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACnB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;;AAEzE,SAAC,CAAC;;IAGI,mBAAmB,GAAA;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;AAC7C,YAAA,IAAI,EAAE,CAAC,YAAY,MAAM,CAAC;gBAAE;;AAE5B,YAAA,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACd,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,KAAK,KAAK,EAAE;oBACpD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;qBACzC,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,KAAK,SAAS,EAAE;oBAC/D,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;;;;iBAG/C;AACL,gBAAA,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;oBAC1D,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC;;qBACzC,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,KAAK,UAAU,EAAE;oBAChE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;AAGpD,SAAC,CAAC;;IAGI,mBAAmB,CACzB,WAA8C,EAC9C,MAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAW;;;;;;;;;AASrC,YAAA,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;gBAC5B,UAAU,CAAC,OAAO,CAAC;AACnB,gBAAA,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;oBAChD,qBAAqB,CAAC,OAAO,CAAC;;AAElC,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACjB,gBAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAC1B,IAAI,MAAM,CACR,WAAW,EACX,IAAI,CAAC,UAAU,KAAK,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,EACnE,MAAM,CACP,CACF;AACH,aAAC,CAAC;AACJ,SAAC,CAAC;;;IAIJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE;AAC5C,QAAA,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE;;kHAhHnC,cAAc,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAd,cAAc,EAAA,CAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B;;;AC0BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;SACa,aAAa,CAAC,MAAc,EAAE,GAAG,QAA0B,EAAA;AACzE,IAAA,OAAO,wBAAwB,CAAC;QAC9B,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC;AAChD,QAAA,OAAO,SAAS,KAAK,WAAW,IAAI;cAChC,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI;AAC9C,cAAE,EAAE;AACN,QAAA,EAAC,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAC;QAChE,EAAC,OAAO,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,oBAAoB,EAAC;QAChF,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC;AAC9C,KAAA,CAAC;AACJ;AAEM,SAAU,SAAS,CAAC,MAAc,EAAA;AACtC,IAAA,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI;AAChC;AAYA;;AAEG;AACH,SAAS,aAAa,CACpB,IAAiB,EACjB,SAAiD,EAAA;IAEjD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAC;AAC7C;AAEA;;;AAGG;AACI,MAAM,kBAAkB,GAAG,IAAI,cAAc,CAAU,EAAE,EAAE;AAChE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,KAAK;AACrB,CAAA,CAAC;AAEF,MAAM,4BAA4B,GAAG;AACnC,IAAA,OAAO,EAAE,uBAAuB;AAChC,IAAA,KAAK,EAAE,IAAI;IACX,UAAU,GAAA;AACR,QAAA,OAAO,MAAK;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE;gBAC/B,OAAO,CAAC,IAAI,CACV,gFAAgF;AAC9E,oBAAA,2BAA2B,CAC9B;;AAEL,SAAC;KACF;CACF;AAED;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,aAAa,CAAC,MAAc,EAAA;IAC1C,OAAO;QACL,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC;AAChD,QAAA,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,4BAA4B,GAAG,EAAE;KAClF;AACH;AAYA;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACa,SAAA,qBAAqB,CACnC,OAAA,GAAoC,EAAE,EAAA;AAEtC,IAAA,MAAM,SAAS,GAAG;AAChB,QAAA;AACE,YAAA,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACjD,gBAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;AAC3B,gBAAA,MAAM,WAAW,GAAG,MAAM,CAAC,qBAAqB,CAAC;AACjD,gBAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,gBAAA,OAAO,IAAI,cAAc,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,CAAC;aACvF;AACF,SAAA;KACF;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,mDAA6C,SAAS,CAAC;AAC7E;SAEgB,oBAAoB,GAAA;AAClC,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACjC,OAAO,CAAC,wBAA+C,KAAI;QACzD,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;QAExC,IAAI,wBAAwB,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YAClD;;QAGF,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACnC,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;QAElD,IAAI,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAA,CAAA,6CAA2C;YAC7E,MAAM,CAAC,iBAAiB,EAAE;;AAG5B,QAAA,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,EAAE,eAAe,EAAE;AACzE,QAAA,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,EAAE,IAAI,EAAE;QAC7D,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACzB,aAAa,CAAC,IAAI,EAAE;YACpB,aAAa,CAAC,QAAQ,EAAE;YACxB,aAAa,CAAC,WAAW,EAAE;;AAE/B,KAAC;AACH;AAEA;;;;AAIG;AACH,MAAM,cAAc,GAAG,IAAI,cAAc,CACvC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,0BAA0B,GAAG,EAAE,EAC/E;IACE,OAAO,EAAE,MAAK;QACZ,OAAO,IAAI,OAAO,EAAQ;KAC3B;AACF,CAAA,CACF;AA0BD,MAAM,kBAAkB,GAAG,IAAI,cAAc,CAC3C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,oBAAoB,GAAG,EAAE,EACzE,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAA0C,CAAA,6CAAC,CAC1E;AA4BD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;SACa,oCAAoC,GAAA;AAClD,IAAA,MAAM,SAAS,GAAG;AAChB,QAAA,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,6CAAoC;QAC1E,qBAAqB,CAAC,MAAK;AACzB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,YAAA,MAAM,mBAAmB,GAAiB,QAAQ,CAAC,GAAG,CACpD,oBAAoB,EACpB,OAAO,CAAC,OAAO,EAAE,CAClB;AAED,YAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,MAAK;AACnC,gBAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;oBAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnC,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;AAClD,oBAAA,mBAAmB,CAAC,MAAM,EAAE,MAAK;;;wBAG/B,OAAO,CAAC,IAAI,CAAC;AACf,qBAAC,CAAC;oBAEF,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,kBAAkB,GAAG,MAAK;;;;wBAI5D,OAAO,CAAC,IAAI,CAAC;AACb,wBAAA,OAAO,aAAa,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,aAAa;AAC1D,qBAAC;oBACD,MAAM,CAAC,iBAAiB,EAAE;AAC5B,iBAAC,CAAC;AACJ,aAAC,CAAC;AACJ,SAAC,CAAC;KACH;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,kEAA4D,SAAS,CAAC;AAC5F;AAcA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;SACa,6BAA6B,GAAA;AAC3C,IAAA,MAAM,SAAS,GAAG;QAChB,qBAAqB,CAAC,MAAK;AACzB,YAAA,MAAM,CAAC,MAAM,CAAC,CAAC,2BAA2B,EAAE;AAC9C,SAAC,CAAC;AACF,QAAA,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,sCAA6B;KACpE;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,2DAAqD,SAAS,CAAC;AACrF;AAYA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;SACa,gBAAgB,GAAA;IAC9B,IAAI,SAAS,GAAe,EAAE;AAC9B,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,SAAS,GAAG;AACV,YAAA;AACE,gBAAA,OAAO,EAAE,uBAAuB;AAChC,gBAAA,KAAK,EAAE,IAAI;gBACX,UAAU,EAAE,MAAK;AACf,oBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,oBAAA,OAAO,MACL,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAQ,KAAI;;AAEnC,wBAAA,OAAO,CAAC,KAAK,GAAG,CAAuB,cAAA,EAAA,CAAC,CAAC,WAAY,CAAC,IAAI,CAAE,CAAA,CAAC;wBAC7D,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9B,wBAAA,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AACd,wBAAA,OAAO,CAAC,QAAQ,IAAI;;AAEtB,qBAAC,CAAC;iBACL;AACF,aAAA;SACF;;SACI;QACL,SAAS,GAAG,EAAE;;AAEhB,IAAA,OAAO,aAAa,CAAA,CAAA,8CAAwC,SAAS,CAAC;AACxE;AAEA,MAAM,gBAAgB,GAAG,IAAI,cAAc,CACzC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,kBAAkB,GAAG,EAAE,CACxE;AAaD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACG,SAAU,cAAc,CAAC,kBAA4C,EAAA;AACzE,IAAA,MAAM,SAAS,GAAG;AAChB,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAC;AACzD,QAAA,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAC;KAC/D;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,4CAAsC,SAAS,CAAC;AACtE;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACG,SAAU,gBAAgB,CAAC,OAA4B,EAAA;AAC3D,IAAA,MAAM,SAAS,GAAG,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,OAAO,EAAC,CAAC;AACtE,IAAA,OAAO,aAAa,CAAA,CAAA,qDAA+C,SAAS,CAAC;AAC/E;AAYA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;SACa,gBAAgB,GAAA;AAC9B,IAAA,MAAM,SAAS,GAAG,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC,CAAC;AAC/E,IAAA,OAAO,aAAa,CAAA,CAAA,oDAA8C,SAAS,CAAC;AAC9E;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;AACG,SAAU,0BAA0B,CACxC,OAA8D,EAAA;AAE9D,IAAA,MAAM,SAAS,GAAG;AAChB,QAAA;AACE,YAAA,OAAO,EAAE,wBAAwB;AACjC,YAAA,QAAQ,EAAE,OAAO;AAClB,SAAA;KACF;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,wDAAkD,SAAS,CAAC;AAClF;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;SACa,yBAAyB,GAAA;AACvC,IAAA,MAAM,SAAS,GAAG;QAChB,0BAA0B;AAC1B,QAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,0BAA0B,EAAC;KACjE;AAED,IAAA,OAAO,aAAa,CAAA,CAAA,uDAAiD,SAAS,CAAC;AACjF;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACG,SAAU,mBAAmB,CACjC,OAAuC,EAAA;IAEvCC,uBAAsB,CAAC,yBAAyB,CAAC;AACjD,IAAA,MAAM,SAAS,GAAG;AAChB,QAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AACjE,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;AAChC,YAAA,QAAQ,EAAE,EAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,EAAE,qBAAqB,EAAE,GAAG,OAAO,EAAC;AAC7E,SAAA;KACF;AACD,IAAA,OAAO,aAAa,CAAA,CAAA,iDAA2C,SAAS,CAAC;AAC3E;;AC3tBA;;AAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAEC,qBAAoB,CAAC;AAE5F;;AAEG;AACI,MAAM,oBAAoB,GAAG,IAAI,cAAc,CACpD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,gCAAgC,GAAG,EAAE,CACtF;AAED;AACA;AACA;AACA;AACa,MAAA,gBAAgB,GAAe;IAC1C,QAAQ;AACR,IAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,oBAAoB,EAAC;IACxD,MAAM;IACN,sBAAsB;AACtB,IAAA,EAAC,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAC;IAChE,kBAAkB;;;AAGlB,IAAA,OAAO,SAAS,KAAK,WAAW,IAAI;UAChC,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI;AAC9C,UAAE,EAAE;;AAGR;;;;;;;;;;;;;;;;;;;;AAoBG;MAKU,YAAY,CAAA;AACvB,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,MAAM,CAAC,oBAAoB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;;AAIlD;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,OAAO,OAAO,CAAC,MAAc,EAAE,MAAqB,EAAA;QAClD,OAAO;AACL,YAAA,QAAQ,EAAE,YAAY;AACtB,YAAA,SAAS,EAAE;gBACT,gBAAgB;AAChB,gBAAA,OAAO,SAAS,KAAK,WAAW,IAAI;sBAChC,MAAM,EAAE;AACR,0BAAE,gBAAgB,EAAE,CAAC;AACrB,0BAAE;AACJ,sBAAE,EAAE;gBACN,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC;AAChD,gBAAA,OAAO,SAAS,KAAK,WAAW,IAAI;AAClC,sBAAE;AACE,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,UAAU,EAAE,mBAAmB;AAC/B,wBAAA,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;AACjD;AACH,sBAAE,EAAE;AACN,gBAAA,MAAM,EAAE;AACN,sBAAE;AACE,wBAAA,OAAO,EAAE,wBAAwB;wBACjC,QAAQ,EAAE,MAAM,CAAC,YAAY;AAC9B;AACH,sBAAE,EAAE;AACN,gBAAA,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,EAAE,EAAC;gBAC/D,MAAM,EAAE,OAAO,GAAG,2BAA2B,EAAE,GAAG,2BAA2B,EAAE;AAC/E,gBAAA,qBAAqB,EAAE;AACvB,gBAAA,MAAM,EAAE,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,UAAU,GAAG,EAAE;AACtF,gBAAA,MAAM,EAAE,iBAAiB,GAAG,wBAAwB,CAAC,MAAM,CAAC,GAAG,EAAE;AACjE,gBAAA,MAAM,EAAE,qBAAqB,GAAG,yBAAyB,EAAE,CAAC,UAAU,GAAG,EAAE;AAC3E,gBAAA,MAAM,EAAE,qBAAqB,GAAG,mBAAmB,EAAE,CAAC,UAAU,GAAG,EAAE;AACrE,gBAAA,wBAAwB,EAAE;AAC3B,aAAA;SACF;;AAGH;;;;;;;;;;;;;;;AAeG;IACH,OAAO,QAAQ,CAAC,MAAc,EAAA;QAC5B,OAAO;AACL,YAAA,QAAQ,EAAE,YAAY;AACtB,YAAA,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC,CAAC;SAC9D;;kHAjFQ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EApDE,OAAA,EAAA,CAAA,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAEA,qBAAoB,CAAA,EAAA,OAAA,EAAA,CAAhE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAEA,qBAAoB,CAAA,EAAA,CAAA;mHAoD9E,YAAY,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,OAAO,EAAE,iBAAiB;AAC3B,iBAAA;;AAsFD;;;AAGG;SACa,qBAAqB,GAAA;IACnC,OAAO;AACL,QAAA,OAAO,EAAE,eAAe;QACxB,UAAU,EAAE,MAAK;AACf,YAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACjD,YAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;AAC3B,YAAA,MAAM,MAAM,GAAiB,MAAM,CAAC,oBAAoB,CAAC;AACzD,YAAA,MAAM,WAAW,GAAG,MAAM,CAAC,qBAAqB,CAAC;AACjD,YAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,YAAA,IAAI,MAAM,CAAC,YAAY,EAAE;AACvB,gBAAA,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;;AAEjD,YAAA,OAAO,IAAI,cAAc,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAC;SACtF;KACF;AACH;AAEA;AACA;AACA,SAAS,2BAA2B,GAAA;IAClC,OAAO,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AACpE;AAEA;AACA;AACA,SAAS,2BAA2B,GAAA;IAClC,OAAO,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AACpE;AAEM,SAAU,mBAAmB,CAAC,MAAc,EAAA;IAChD,IAAI,MAAM,EAAE;QACV,MAAM,IAAIL,aAAY,CAAA,IAAA,+CAEpB,CAA4G,0GAAA,CAAA;AAC1G,YAAA,CAAA,gEAAA,CAAkE,CACrE;;AAEH,IAAA,OAAO,SAAS;AAClB;AAEA;AACA;AACA,SAAS,wBAAwB,CAAC,MAA+C,EAAA;IAC/E,OAAO;AACL,QAAA,MAAM,CAAC,iBAAiB,KAAK,UAAU,GAAG,6BAA6B,EAAE,CAAC,UAAU,GAAG,EAAE;QACzF,MAAM,CAAC,iBAAiB,KAAK;AAC3B,cAAE,oCAAoC,EAAE,CAAC;AACzC,cAAE,EAAE;KACP;AACH;AAEA;AACA;;;;;AAKG;MACU,kBAAkB,GAAG,IAAI,cAAc,CAClD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,oBAAoB,GAAG,EAAE;AAG3E,SAAS,wBAAwB,GAAA;IAC/B,OAAO;;;AAGL,QAAA,EAAC,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,oBAAoB,EAAC;QAC/D,EAAC,OAAO,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAC;KAChF;AACH;;;;"}