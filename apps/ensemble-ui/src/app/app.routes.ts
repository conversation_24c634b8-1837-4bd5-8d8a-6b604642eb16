import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'loan-search',
    loadComponent: () =>
      import('./[pages]/loan-search/loan-search.component').then(
        (c) => c.<PERSON>omponent
      )
  },
  {
    path: 'loan-details/:id',
    loadComponent: () =>
      import('./[pages]/loan-details/loan-details.component').then(
        (c) => c.LoanDetailsComponent
      )
  },
  {
    path: 'loan-status/:loanNumber',
    loadComponent: () =>
      import('./[pages]/loan-status/loan-status.component').then(
        (c) => c.<PERSON><PERSON>tatusComponent
      )
  },
  {
    path: 'change-history',
    loadComponent: () =>
      import('./[pages]/change-history/change-history.component').then(
        (c) => c.ChangeHistoryComponent
      )
  },
  {
    path: 'log',
    loadComponent: () =>
      import('./[pages]/log/log.component').then(
        (c) => c.LogComponent
      )
  },
  {
    path: '',
    redirectTo: '/loan-search',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/loan-search'
  }
];
