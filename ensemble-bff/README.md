# Ensemble BFF (Backend For Frontend)

NestJS backend API service for the Rocket Logic Ensemble platform.

## Overview

The Ensemble BFF serves as the backend API layer for the Rocket Logic Ensemble application. It acts as a middleware between the frontend and various loan systems, providing:

- A unified API for frontend consumption
- Data transformation and normalization
- Authentication and authorization
- System integration and orchestration
- Business logic validation

## Key Features

- **Loan Data Integration**: Connect to multiple loan systems and normalize data
- **Change History**: Track and record all modifications to loans
- **Security Layer**: Enforce permissions and validate operations
- **Data Validation**: Ensure data integrity across systems
- **Error Handling**: Standardized error responses and logging

## Architecture

The application follows a modular architecture using NestJS:

### Core Structure

```
src/
├── app.module.ts             # Main application module
├── main.ts                   # Application entry point
├── environments/             # Environment configuration
├── modules/                  # Feature modules
│   ├── loans/                # Loan management
│   ├── change-history/       # Change tracking
│   ├── users/                # User management
│   └── system-integration/   # External system integration
├── shared/                   # Shared modules and utilities
│   ├── guards/               # Authentication guards
│   ├── interceptors/         # Request/response interceptors
│   ├── filters/              # Exception filters
│   └── utils/                # Utility functions
```

## API Endpoints

### Loan Management

- `GET /loans`: Search loans with filtering
- `GET /loans/:id`: Get loan details
- `POST /loans`: Create a new loan
- `PUT /loans/:id`: Update loan information
- `PATCH /loans/:id/status`: Update loan status

### Change History

- `GET /loans/:id/history`: Get loan change history
- `POST /loans/:id/history`: Record a new change event
- `GET /history`: Global change history with filtering

### User Management

- `GET /users/me`: Get current user profile
- `GET /users/:id/permissions`: Get user permissions
- `POST /auth/login`: Authenticate user
- `POST /auth/logout`: End user session

## Integration Points

The BFF integrates with multiple systems:

- **Loan Origination System (LOS)**: Primary loan data source
- **Servicing System**: Post-closing loan servicing data
- **Document Management System**: Loan documentation
- **User Management System**: Authentication and permissions
- **Audit System**: Security and compliance tracking

## Development Guidelines

### API Design

- Follow RESTful principles
- Use consistent request/response formats
- Implement proper status codes
- Version APIs with header-based versioning
- Document with Swagger/OpenAPI

### Security

- Implement JWT authentication
- Use role-based access control
- Validate all inputs
- Sanitize outputs to prevent data leakage
- Log security events

### Error Handling

- Use standardized error response format
- Implement global exception filters
- Provide appropriate error details without exposing internals
- Log errors with context for troubleshooting

## Getting Started

### Development

```bash
# Start in development mode
nx serve ensemble-bff

# Start with watch mode
nx serve ensemble-bff --watch
```

### Testing

```bash
# Run unit tests
nx test ensemble-bff

# Run e2e tests
nx e2e ensemble-bff-e2e
```

### Building

```bash
# Build for production
nx build ensemble-bff
```

## Configuration

The application uses environment-based configuration:

- `.env.development`: Development settings
- `.env.test`: Testing settings
- `.env.production`: Production settings

Key configuration includes:

- Database connection details
- External system endpoints
- Authentication settings
- Logging configuration