{"$schema": "http://json-schema.org/draft-07/schema", "$id": "ArchitectTargets", "title": "Targets schema for validating Architect targets configuration.", "type": "object", "description": "A map of available project targets.", "additionalProperties": {"$ref": "#/definitions/target"}, "required": [], "definitions": {"target": {"type": "object", "description": "Target options.", "properties": {"builder": {"type": "string", "description": "The builder used for this package."}, "options": {"$ref": "#/definitions/options"}, "configurations": {"type": "object", "description": "A map of alternative target options.", "additionalProperties": {"$ref": "#/definitions/options"}}}, "additionalProperties": false, "required": ["builder", "options"]}, "options": {"type": "object", "description": "Target options."}}}