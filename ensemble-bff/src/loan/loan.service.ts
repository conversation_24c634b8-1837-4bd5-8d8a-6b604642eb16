import { Injectable, NotFoundException } from '@nestjs/common';
import { 
  LoanSearchDto, 
  CreateLoanDto, 
  UpdateLoanDto, 
  LoanResponseDto, 
  LoanSearchResponseDto 
} from '@rocket-logic-ensemble/models';
import { LoanStatus, LoanType, EmploymentStatus } from '@rocket-logic-ensemble/models';

@Injectable()
export class LoanService {
  // Mock data for development - in production this would connect to a database
  private loans: LoanResponseDto[] = [
    {
      id: '1',
      loanNumber: '1234567890',
      borrowerName: '<PERSON>',
      borrowerEmail: '<EMAIL>',
      loanAmount: 350000,
      interestRate: 6.5,
      loanTerm: 360,
      status: LoanStatus.UNDER_REVIEW,
      applicationDate: '2024-01-15T10:00:00Z',
      propertyAddress: '123 Main St, Anytown, ST 12345',
      propertyValue: 400000,
      downPayment: 70000,
      monthlyPayment: 2212.24,
      loanType: LoanType.CONVENTIONAL,
      creditScore: 750,
      debtToIncomeRatio: 28,
      employmentStatus: EmploymentStatus.EMPLOYED,
      annualIncome: 95000,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      loanNumber: '2345678901',
      borrowerName: 'Jane Smith',
      borrowerEmail: '<EMAIL>',
      loanAmount: 275000,
      interestRate: 6.25,
      loanTerm: 360,
      status: LoanStatus.APPROVED,
      applicationDate: '2024-01-10T14:30:00Z',
      approvalDate: '2024-01-20T09:15:00Z',
      propertyAddress: '456 Oak Ave, Somewhere, ST 67890',
      propertyValue: 320000,
      downPayment: 55000,
      monthlyPayment: 1693.54,
      loanType: LoanType.FHA,
      creditScore: 680,
      debtToIncomeRatio: 32,
      employmentStatus: EmploymentStatus.EMPLOYED,
      annualIncome: 72000,
      createdAt: '2024-01-10T14:30:00Z',
      updatedAt: '2024-01-20T09:15:00Z'
    },
    {
      id: '3',
      loanNumber: '3456789012',
      borrowerName: 'Bob Johnson',
      borrowerEmail: '<EMAIL>',
      loanAmount: 450000,
      interestRate: 6.75,
      loanTerm: 360,
      status: LoanStatus.FUNDED,
      applicationDate: '2023-12-05T11:20:00Z',
      approvalDate: '2023-12-15T16:45:00Z',
      closingDate: '2024-01-05T13:30:00Z',
      propertyAddress: '789 Pine Rd, Elsewhere, ST 13579',
      propertyValue: 500000,
      downPayment: 90000,
      monthlyPayment: 2921.13,
      loanType: LoanType.JUMBO,
      creditScore: 780,
      debtToIncomeRatio: 25,
      employmentStatus: EmploymentStatus.SELF_EMPLOYED,
      annualIncome: 140000,
      createdAt: '2023-12-05T11:20:00Z',
      updatedAt: '2024-01-05T13:30:00Z'
    }
  ];

  async searchLoans(searchDto: LoanSearchDto): Promise<LoanSearchResponseDto> {
    let filteredLoans = [...this.loans];

    // Apply filters
    if (searchDto.loanNumber) {
      filteredLoans = filteredLoans.filter(loan => 
        loan.loanNumber.includes(searchDto.loanNumber)
      );
    }

    if (searchDto.borrowerName) {
      filteredLoans = filteredLoans.filter(loan => 
        loan.borrowerName.toLowerCase().includes(searchDto.borrowerName.toLowerCase())
      );
    }

    if (searchDto.borrowerEmail) {
      filteredLoans = filteredLoans.filter(loan => 
        loan.borrowerEmail?.toLowerCase().includes(searchDto.borrowerEmail.toLowerCase())
      );
    }

    if (searchDto.status) {
      filteredLoans = filteredLoans.filter(loan => loan.status === searchDto.status);
    }

    if (searchDto.loanType) {
      filteredLoans = filteredLoans.filter(loan => loan.loanType === searchDto.loanType);
    }

    if (searchDto.minAmount) {
      filteredLoans = filteredLoans.filter(loan => loan.loanAmount >= searchDto.minAmount);
    }

    if (searchDto.maxAmount) {
      filteredLoans = filteredLoans.filter(loan => loan.loanAmount <= searchDto.maxAmount);
    }

    if (searchDto.applicationDateFrom) {
      const fromDate = new Date(searchDto.applicationDateFrom);
      filteredLoans = filteredLoans.filter(loan => 
        new Date(loan.applicationDate) >= fromDate
      );
    }

    if (searchDto.applicationDateTo) {
      const toDate = new Date(searchDto.applicationDateTo);
      filteredLoans = filteredLoans.filter(loan => 
        new Date(loan.applicationDate) <= toDate
      );
    }

    // Apply sorting
    const sortBy = searchDto.sortBy || 'applicationDate';
    const sortOrder = searchDto.sortOrder || 'desc';

    filteredLoans.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      // Handle date sorting
      if (sortBy.includes('Date')) {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // Apply pagination
    const page = searchDto.page || 1;
    const pageSize = searchDto.pageSize || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedLoans = filteredLoans.slice(startIndex, endIndex);

    return {
      loans: paginatedLoans,
      totalCount: filteredLoans.length,
      page,
      pageSize,
      hasMore: endIndex < filteredLoans.length
    };
  }

  async findLoanByNumber(loanNumber: string): Promise<LoanResponseDto> {
    const loan = this.loans.find(l => l.loanNumber === loanNumber);
    if (!loan) {
      throw new NotFoundException(`Loan with number ${loanNumber} not found`);
    }
    return loan;
  }

  async findLoanById(id: string): Promise<LoanResponseDto> {
    const loan = this.loans.find(l => l.id === id);
    if (!loan) {
      throw new NotFoundException(`Loan with ID ${id} not found`);
    }
    return loan;
  }

  async createLoan(createLoanDto: CreateLoanDto): Promise<LoanResponseDto> {
    const newLoan: LoanResponseDto = {
      id: (this.loans.length + 1).toString(),
      ...createLoanDto,
      status: LoanStatus.DRAFT,
      applicationDate: new Date().toISOString(),
      monthlyPayment: this.calculateMonthlyPayment(
        createLoanDto.loanAmount,
        createLoanDto.interestRate,
        createLoanDto.loanTerm
      ),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.loans.push(newLoan);
    return newLoan;
  }

  async updateLoan(id: string, updateLoanDto: UpdateLoanDto): Promise<LoanResponseDto> {
    const loanIndex = this.loans.findIndex(l => l.id === id);
    if (loanIndex === -1) {
      throw new NotFoundException(`Loan with ID ${id} not found`);
    }

    const updatedLoan = {
      ...this.loans[loanIndex],
      ...updateLoanDto,
      updatedAt: new Date().toISOString()
    };

    // Recalculate monthly payment if loan amount, interest rate, or term changed
    if (updateLoanDto.loanAmount || updateLoanDto.interestRate || updateLoanDto.loanTerm) {
      updatedLoan.monthlyPayment = this.calculateMonthlyPayment(
        updatedLoan.loanAmount,
        updatedLoan.interestRate,
        updatedLoan.loanTerm
      );
    }

    this.loans[loanIndex] = updatedLoan;
    return updatedLoan;
  }

  private calculateMonthlyPayment(principal: number, annualRate: number, termInMonths: number): number {
    const monthlyRate = annualRate / 100 / 12;
    
    if (monthlyRate === 0) {
      return principal / termInMonths;
    }

    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) /
      (Math.pow(1 + monthlyRate, termInMonths) - 1);

    return Math.round(monthlyPayment * 100) / 100;
  }
}
