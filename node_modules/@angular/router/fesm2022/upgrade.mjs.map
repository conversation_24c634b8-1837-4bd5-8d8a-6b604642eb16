{"version": 3, "file": "upgrade.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/router/upgrade/src/upgrade.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Location} from '@angular/common';\nimport {APP_BOOTSTRAP_LISTENER, ComponentRef, InjectionToken} from '@angular/core';\nimport {Router, ɵRestoredState as RestoredState} from '../../index';\nimport {UpgradeModule} from '@angular/upgrade/static';\n\n/**\n * Creates an initializer that sets up `ngRoute` integration\n * along with setting up the Angular router.\n *\n * @usageNotes\n *\n * For standalone applications:\n * ```ts\n * export const appConfig: ApplicationConfig = {\n *   providers: [RouterUpgradeInitializer],\n * };\n * ```\n *\n * For NgModule based applications:\n * ```ts\n * @NgModule({\n *  imports: [\n *   RouterModule.forRoot(SOME_ROUTES),\n *   UpgradeModule\n * ],\n * providers: [\n *   RouterUpgradeInitializer\n * ]\n * })\n * export class AppModule {\n *   ngDoBootstrap() {}\n * }\n * ```\n *\n * @publicApi\n */\nexport const RouterUpgradeInitializer = {\n  provide: APP_BOOTSTRAP_LISTENER,\n  multi: true,\n  useFactory: locationSyncBootstrapListener as (ngUpgrade: UpgradeModule) => () => void,\n  deps: [UpgradeModule],\n};\n\n/**\n * @internal\n */\nexport function locationSyncBootstrapListener(ngUpgrade: UpgradeModule) {\n  return () => {\n    setUpLocationSync(ngUpgrade);\n  };\n}\n\n/**\n * Sets up a location change listener to trigger `history.pushState`.\n * Works around the problem that `onPopState` does not trigger `history.pushState`.\n * Must be called *after* calling `UpgradeModule.bootstrap`.\n *\n * @param ngUpgrade The upgrade NgModule.\n * @param urlType The location strategy.\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n * @see {@link /api/common/PathLocationStrategy PathLocationStrategy}\n *\n * @publicApi\n */\nexport function setUpLocationSync(\n  ngUpgrade: UpgradeModule,\n  urlType: 'path' | 'hash' = 'path',\n): void {\n  if (!ngUpgrade.$injector) {\n    throw new Error(`\n        RouterUpgradeInitializer can be used only after UpgradeModule.bootstrap has been called.\n        Remove RouterUpgradeInitializer and call setUpLocationSync after UpgradeModule.bootstrap.\n      `);\n  }\n\n  const router: Router = ngUpgrade.injector.get(Router);\n  const location: Location = ngUpgrade.injector.get(Location);\n\n  ngUpgrade.$injector\n    .get('$rootScope')\n    .$on(\n      '$locationChangeStart',\n      (\n        event: any,\n        newUrl: string,\n        oldUrl: string,\n        newState?: {[k: string]: unknown} | RestoredState,\n        oldState?: {[k: string]: unknown} | RestoredState,\n      ) => {\n        // Navigations coming from Angular router have a navigationId state\n        // property. Don't trigger Angular router navigation again if it is\n        // caused by a URL change from the current Angular router\n        // navigation.\n        const currentNavigationId = router.getCurrentNavigation()?.id;\n        const newStateNavigationId = newState?.navigationId;\n        if (newStateNavigationId !== undefined && newStateNavigationId === currentNavigationId) {\n          return;\n        }\n\n        let url;\n        if (urlType === 'path') {\n          url = resolveUrl(newUrl);\n        } else if (urlType === 'hash') {\n          // Remove the first hash from the URL\n          const hashIdx = newUrl.indexOf('#');\n          url = resolveUrl(newUrl.substring(0, hashIdx) + newUrl.substring(hashIdx + 1));\n        } else {\n          throw 'Invalid URLType passed to setUpLocationSync: ' + urlType;\n        }\n        const path = location.normalize(url.pathname);\n        router.navigateByUrl(path + url.search + url.hash);\n      },\n    );\n}\n\n/**\n * Normalizes and parses a URL.\n *\n * - Normalizing means that a relative URL will be resolved into an absolute URL in the context of\n *   the application document.\n * - Parsing means that the anchor's `protocol`, `hostname`, `port`, `pathname` and related\n *   properties are all populated to reflect the normalized URL.\n *\n * While this approach has wide compatibility, it doesn't work as expected on IE. On IE, normalizing\n * happens similar to other browsers, but the parsed components will not be set. (E.g. if you assign\n * `a.href = 'foo'`, then `a.protocol`, `a.host`, etc. will not be correctly updated.)\n * We work around that by performing the parsing in a 2nd step by taking a previously normalized URL\n * and assigning it again. This correctly populates all properties.\n *\n * See\n * https://github.com/angular/angular.js/blob/2c7400e7d07b0f6cec1817dab40b9250ce8ebce6/src/ng/urlUtils.js#L26-L33\n * for more info.\n */\nlet anchor: HTMLAnchorElement | undefined;\nfunction resolveUrl(url: string): {pathname: string; search: string; hash: string} {\n  anchor ??= document.createElement('a');\n\n  anchor.setAttribute('href', url);\n  anchor.setAttribute('href', anchor.href);\n\n  return {\n    // IE does not start `pathname` with `/` like other browsers.\n    pathname: `/${anchor.pathname.replace(/^\\//, '')}`,\n    search: anchor.search,\n    hash: anchor.hash,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACU,MAAA,wBAAwB,GAAG;AACtC,IAAA,OAAO,EAAE,sBAAsB;AAC/B,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,UAAU,EAAE,6BAAyE;IACrF,IAAI,EAAE,CAAC,aAAa,CAAC;;AAGvB;;AAEG;AACG,SAAU,6BAA6B,CAAC,SAAwB,EAAA;AACpE,IAAA,OAAO,MAAK;QACV,iBAAiB,CAAC,SAAS,CAAC;AAC9B,KAAC;AACH;AAEA;;;;;;;;;;;AAWG;SACa,iBAAiB,CAC/B,SAAwB,EACxB,UAA2B,MAAM,EAAA;AAEjC,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC;;;AAGb,MAAA,CAAA,CAAC;;IAGN,MAAM,MAAM,GAAW,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;IACrD,MAAM,QAAQ,GAAa,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;AAE3D,IAAA,SAAS,CAAC;SACP,GAAG,CAAC,YAAY;AAChB,SAAA,GAAG,CACF,sBAAsB,EACtB,CACE,KAAU,EACV,MAAc,EACd,MAAc,EACd,QAAiD,EACjD,QAAiD,KAC/C;;;;;QAKF,MAAM,mBAAmB,GAAG,MAAM,CAAC,oBAAoB,EAAE,EAAE,EAAE;AAC7D,QAAA,MAAM,oBAAoB,GAAG,QAAQ,EAAE,YAAY;QACnD,IAAI,oBAAoB,KAAK,SAAS,IAAI,oBAAoB,KAAK,mBAAmB,EAAE;YACtF;;AAGF,QAAA,IAAI,GAAG;AACP,QAAA,IAAI,OAAO,KAAK,MAAM,EAAE;AACtB,YAAA,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC;;AACnB,aAAA,IAAI,OAAO,KAAK,MAAM,EAAE;;YAE7B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;YACnC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;;aACzE;YACL,MAAM,+CAA+C,GAAG,OAAO;;QAEjE,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC7C,QAAA,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC;AACpD,KAAC,CACF;AACL;AAEA;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAI,MAAqC;AACzC,SAAS,UAAU,CAAC,GAAW,EAAA;AAC7B,IAAA,MAAM,KAAK,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AAEtC,IAAA,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;IAChC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;IAExC,OAAO;;AAEL,QAAA,QAAQ,EAAE,CAAA,CAAA,EAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE,CAAA;QAClD,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC,IAAI;KAClB;AACH;;;;"}