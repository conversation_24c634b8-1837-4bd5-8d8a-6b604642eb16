import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Param, 
  Body, 
  Query, 
  HttpCode, 
  HttpStatus,
  ValidationPipe
} from '@nestjs/common';
import { LoanService } from './loan.service';
import { 
  LoanSearchDto, 
  CreateLoanDto, 
  UpdateLoanDto, 
  LoanResponseDto, 
  LoanSearchResponseDto 
} from '@rocket-logic-ensemble/models';

@Controller('api/loans')
export class LoanController {
  constructor(private readonly loanService: LoanService) {}

  @Get('search')
  async searchLoans(
    @Query(new ValidationPipe({ transform: true })) searchDto: LoanSearchDto
  ): Promise<LoanSearchResponseDto> {
    return this.loanService.searchLoans(searchDto);
  }

  @Get('by-number/:loanNumber')
  async findLoanByNumber(@Param('loanNumber') loanNumber: string): Promise<LoanResponseDto> {
    return this.loanService.findLoanByNumber(loanNumber);
  }

  @Get(':id')
  async findLoanById(@Param('id') id: string): Promise<LoanResponseDto> {
    return this.loanService.findLoanById(id);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createLoan(
    @Body(new ValidationPipe()) createLoanDto: CreateLoanDto
  ): Promise<LoanResponseDto> {
    return this.loanService.createLoan(createLoanDto);
  }

  @Put(':id')
  async updateLoan(
    @Param('id') id: string,
    @Body(new ValidationPipe()) updateLoanDto: UpdateLoanDto
  ): Promise<LoanResponseDto> {
    return this.loanService.updateLoan(id, updateLoanDto);
  }
}
