import { Injectable, signal } from '@angular/core';

export interface LayoutState {
  sideMenuOpen: boolean;
  bottomSheetOpen: boolean;
  contextualPanelOpen: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LayoutService {
  private state = signal<LayoutState>({
    sideMenuOpen: false,
    bottomSheetOpen: false,
    contextualPanelOpen: false
  });

  // Getters for reactive state
  get sideMenuOpen() {
    return this.state().sideMenuOpen;
  }

  get bottomSheetOpen() {
    return this.state().bottomSheetOpen;
  }

  get contextualPanelOpen() {
    return this.state().contextualPanelOpen;
  }

  // State management methods
  openSideMenu() {
    this.state.update(state => ({ ...state, sideMenuOpen: true }));
  }

  closeSideMenu() {
    this.state.update(state => ({ ...state, sideMenuOpen: false }));
  }

  toggleSideMenu() {
    this.state.update(state => ({ ...state, sideMenuOpen: !state.sideMenuOpen }));
  }

  openBottomSheet() {
    this.state.update(state => ({ ...state, bottomSheetOpen: true }));
  }

  closeBottomSheet() {
    this.state.update(state => ({ ...state, bottomSheetOpen: false }));
  }

  toggleBottomSheet() {
    this.state.update(state => ({ ...state, bottomSheetOpen: !state.bottomSheetOpen }));
  }

  openContextualPanel() {
    this.state.update(state => ({ ...state, contextualPanelOpen: true }));
  }

  closeContextualPanel() {
    this.state.update(state => ({ ...state, contextualPanelOpen: false }));
  }

  toggleContextualPanel() {
    this.state.update(state => ({ ...state, contextualPanelOpen: !state.contextualPanelOpen }));
  }

  // Close all overlays
  closeAllOverlays() {
    this.state.set({
      sideMenuOpen: false,
      bottomSheetOpen: false,
      contextualPanelOpen: false
    });
  }

  // Get the full state signal for reactive access
  getState() {
    return this.state.asReadonly();
  }
}
