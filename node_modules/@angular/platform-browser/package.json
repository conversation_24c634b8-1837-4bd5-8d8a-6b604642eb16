{"name": "@angular/platform-browser", "version": "20.0.3", "description": "Angular - library for using Angular in a web browser", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/animations": "20.0.3", "@angular/core": "20.0.3", "@angular/common": "20.0.3"}, "peerDependenciesMeta": {"@angular/animations": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/platform-browser"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/platform-browser.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/platform-browser.mjs"}, "./animations": {"types": "./animations/index.d.ts", "default": "./fesm2022/animations.mjs"}, "./animations/async": {"types": "./animations/async/index.d.ts", "default": "./fesm2022/animations/async.mjs"}, "./testing": {"types": "./testing/index.d.ts", "default": "./fesm2022/testing.mjs"}}}