# Ensemble UI

Angular frontend application for the Rocket Logic Ensemble platform.

## Overview

The Ensemble UI is a modern Angular application designed to provide loan support teams with a comprehensive tool for managing loans across multiple systems. The application offers a clean, intuitive interface for searching, viewing, and modifying loan information while maintaining strict access controls and validation.

## Key Features

- **Loan Search**: Find loans by various identifiers
- **Change History**: View detailed timeline of loan modifications
- **Multi-system State Comparison**: View and reconcile differences between systems
- **Inline Editing**: Make changes directly in the UI with validation
- **Security Controls**: Role-based access for sensitive operations

## Architecture

The application follows a feature-based architecture with the following organization:

### Core Structure

- **[components]**: Reusable UI components
- **[containers]**: Smart components that manage state
- **[layout]**: Layout components including main application shell
- **[pages]**: Feature pages that are directly routable
- **change-history**: Feature module for showing loan change history
- **shared**: Shared utilities, directives, and pipes

### State Management

The application uses a combination of:
- Angular services with RxJS for local state
- HTTP services for server communication
- Route params for navigation state

## Development Guidelines

### Component Organization

- Use standalone components with explicit imports
- Follow Angular Material design patterns
- Smart containers handle data fetching and mutation
- Presentational components focus on UI rendering

### Styling

- Use SCSS with appropriate nesting
- Follow BEM-style class naming conventions
- Use Angular Material components and theming

### Testing

- Unit test components with Jest
- E2E tests with Playwright
- Use mock data files for testing services

## Key User Flows

1. **Loan Search**: User enters loan identifier → views loan summary
2. **Change History**: User views loan → checks timeline → filters changes
3. **Edit Loan**: User views loan → edits field → submits change → views updated timeline
4. **Compare Systems**: User views loan → compares states → resolves differences

## Feature Documentation

More detailed documentation for specific features can be found in their respective folders:

- [Pages](./src/app/[pages]/README.md)
- [Change History](./src/app/change-history/README.md)
- [Shared Components](./src/app/shared/README.md)
- [Layout Components](./src/app/[layout]/README.md)