{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"], "sharedGlobals": []}, "workspaceLayout": {"appsDir": ".", "libsDir": "libs"}, "projects": {"ensemble-ui": "ensemble-ui", "ensemble-bff": "ensemble-bff", "ensemble-ui-e2e": "ensemble-ui-e2e", "ensemble-bff-e2e": "ensemble-bff-e2e", "models": "libs/models", "ui": "libs/ui"}, "targetDefaults": {"build": {"cache": true}, "lint": {"cache": true}, "test": {"cache": true}, "e2e": {"cache": true}}, "installation": {"version": "latest", "packageManager": "npm"}}