.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.header-card {
  padding: 0;
  margin-bottom: 1rem;
  border-radius: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
}

.branding h2 {
  margin: 0;
  font-weight: 500;
}

.navigation {
  display: flex;
  gap: 1rem;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 1rem;
  margin-bottom: 1rem;
}

.sidenav-container {
  width: 250px;
  overflow-y: auto;
}

.sidenav-card {
  height: 100%;
  padding: 0;
}

mat-nav-list {
  padding-top: 0;
}

mat-icon {
  margin-right: 0.75rem;
}

.main-content {
  flex: 1;
  overflow-y: auto;
}

.footer-card {
  padding: 0.75rem 1.5rem;
  border-radius: 0;
}

.footer-content {
  text-align: center;
}

.footer-content p {
  margin: 0;
}