import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-shell',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    MatExpansionModule,
    MatCardModule,
    MatListModule
  ],
  templateUrl: './shell.component.html',
  styleUrl: './shell.component.scss'
})
export class ShellComponent {
  appTitle = 'Rocket Logic Ensemble';
  
  // For menu handling
  isOpen = false;
  isOpen2 = false;
  
  onOpen() {
    this.isOpen = true;
  }
  
  onClose() {
    this.isOpen = false;
  }
  
  onOpen2() {
    this.isOpen2 = true;
  }
  
  onClose2() {
    this.isOpen2 = false;
  }
}