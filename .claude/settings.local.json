{"permissions": {"allow": ["Bash(claude mcp add:*)", "Bash(/rds:list-rds-tools)", "Bash(claude code /rds:list-rds-tools)", "mcp__rds__all-rds-tools", "mcp__rds__button-primary-instructions", "mcp__rds__card-instructions", "<PERSON><PERSON>(mkdir:*)", "mcp__rds__rkt-masthead-instructions", "mcp__rds__rkt-sidenav-instructions", "mcp__rds__rkt-footer-instructions", "Bash(npx nx serve:*)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(npx nx build:*)", "mcp__rds__text-input-instructions", "mcp__rds__search-instructions", "mcp__rds__date-instructions", "mcp__rds__date-picker-instructions", "mcp__rds__select-instructions", "mcp__rds__autocomplete-instructions", "mcp__rds__button-secondary-instructions", "mcp__rds__snackbar-instructions", "mcp__rds__rkt-alert-instructions", "mcp__rds__table-instructions", "mcp__rds__spinner-instructions", "mcp__rds__progress-bar-instructions", "mcp__rds__rkt-skeleton-instructions", "Bash(npx nx reset:*)", "Bash(cp:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["rds"]}