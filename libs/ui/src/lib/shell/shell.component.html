<div class="app-container">
  <!-- Header -->
  <mat-card class="header-card">
    <div class="header-content">
      <div class="branding">
        <h2>{{ appTitle }}</h2>
      </div>
      
      <div class="navigation">
        <button mat-button>Dashboard</button>
        <button mat-button>Reports</button>
        <button mat-button>Analytics</button>
        <button mat-button>Settings</button>
      </div>
    </div>
  </mat-card>

  <div class="app-content">
    <!-- Sidenav -->
    <div class="sidenav-container">
      <mat-card class="sidenav-card">
        <mat-nav-list>
          <a mat-list-item>
            <mat-icon>home</mat-icon>
            <span>Home</span>
          </a>
          <a mat-list-item>
            <mat-icon>dashboard</mat-icon>
            <span>Dashboard</span>
          </a>
          <mat-divider></mat-divider>
          <a mat-list-item>
            <mat-icon>assessment</mat-icon>
            <span>Reports</span>
          </a>
          <a mat-list-item>
            <mat-icon>analytics</mat-icon>
            <span>Analytics</span>
          </a>
          <mat-divider></mat-divider>
          <a mat-list-item>
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </a>
          <a mat-list-item>
            <mat-icon>help</mat-icon>
            <span>Help</span>
          </a>
        </mat-nav-list>
      </mat-card>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <router-outlet></router-outlet>
    </div>
  </div>

  <!-- Footer -->
  <mat-card class="footer-card">
    <div class="footer-content">
      <p>© 2024 Rocket Logic Ensemble. All rights reserved.</p>
    </div>
  </mat-card>
</div>