<div class="layout-container">
  <!-- Mini<PERSON> -->
  <header class="layout-header">
    <div class="header-content">
      <!-- Project Name -->
      <div class="project-branding">
        <h1 class="project-name">{{ appTitle }}</h1>
      </div>

      <!-- Contextual Header Actions (for future projection) -->
      <div class="header-actions">
        <ng-content select="[slot=header-actions]"></ng-content>
      </div>

      <!-- Account Menu -->
      <div class="account-menu">
        <button
          mat-button
          [matMenuTriggerFor]="accountMenu"
          class="account-button"
        >
          <div class="user-avatar">{{ user.avatar }}</div>
          <div class="user-info">
            <span class="user-name">{{ user.name }}</span>
          </div>
          <mat-icon>keyboard_arrow_down</mat-icon>
        </button>

        <mat-menu #accountMenu="matMenu" class="account-dropdown">
          <div class="account-header">
            <div class="user-avatar-large">{{ user.avatar }}</div>
            <div class="user-details">
              <div class="user-name-large">{{ user.name }}</div>
              <div class="user-email">{{ user.email }}</div>
              <div class="user-role-badge">{{ user.role }}</div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>

          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </header>

  <!-- Side Menu Overlay (for future projection) -->
  <aside class="side-menu-overlay" [class.open]="sideMenuOpen">
    <div class="side-menu-backdrop" (click)="closeSideMenu()"></div>
    <div class="side-menu-panel">
      <ng-content select="[slot=side-menu]"></ng-content>
    </div>
  </aside>

  <!-- Responsive Main Content -->
  <main class="layout-main">
    <!-- Main Content Area -->
    <div class="main-content-area">
      <ng-content></ng-content>
    </div>

    <!-- Contextual Panels (for future projection) -->
    <div class="contextual-panels">
      <ng-content select="[slot=contextual-panel]"></ng-content>
    </div>
  </main>

  <!-- Bottom Sheet Overlay (for future projection) -->
  <div class="bottom-sheet-overlay" [class.open]="bottomSheetOpen">
    <div class="bottom-sheet-backdrop" (click)="closeBottomSheet()"></div>
    <div class="bottom-sheet-panel">
      <ng-content select="[slot=bottom-sheet]"></ng-content>
    </div>
  </div>
</div>