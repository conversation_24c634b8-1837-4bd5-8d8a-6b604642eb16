{"name": "@angular-devkit/architect", "version": "0.2000.2", "description": "Angular Build Facade", "experimental": true, "main": "src/index.js", "typings": "src/index.d.ts", "dependencies": {"@angular-devkit/core": "20.0.2", "rxjs": "7.8.2"}, "builders": "./builders/builders.json", "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.9", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}