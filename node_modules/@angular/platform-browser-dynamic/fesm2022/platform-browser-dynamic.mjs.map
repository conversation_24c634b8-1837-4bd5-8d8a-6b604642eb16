{"version": 3, "file": "platform-browser-dynamic.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/platform-browser-dynamic/src/version.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/platform-browser-dynamic/src/compiler_factory.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/platform-browser-dynamic/src/resource_loader/resource_loader_impl.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/platform-browser-dynamic/src/platform_providers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('20.0.3');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CompilerConfig} from '@angular/compiler';\nimport {\n  Compiler,\n  CompilerFactory,\n  CompilerOptions,\n  Injector,\n  StaticProvider,\n  ViewEncapsulation,\n} from '@angular/core';\n\nexport const COMPILER_PROVIDERS = <StaticProvider[]>[\n  {provide: Compiler, useFactory: () => new Compiler()},\n];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nexport class JitCompilerFactory implements CompilerFactory {\n  private _defaultOptions: CompilerOptions[];\n\n  /** @internal */\n  constructor(defaultOptions: CompilerOptions[]) {\n    const compilerOptions: CompilerOptions = {\n      defaultEncapsulation: ViewEncapsulation.Emulated,\n    };\n\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n\n  createCompiler(options: CompilerOptions[] = []): Compiler {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [\n        COMPILER_PROVIDERS,\n        {\n          provide: CompilerConfig,\n          useFactory: () => {\n            return new CompilerConfig({\n              defaultEncapsulation: opts.defaultEncapsulation,\n              preserveWhitespaces: opts.preserveWhitespaces,\n            });\n          },\n          deps: [],\n        },\n        opts.providers!,\n      ],\n    });\n    return injector.get(Compiler);\n  }\n}\n\nfunction _mergeOptions(optionsArr: CompilerOptions[]): CompilerOptions {\n  return {\n    defaultEncapsulation: _lastDefined(optionsArr.map((options) => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map((options) => options.providers!)),\n    preserveWhitespaces: _lastDefined(optionsArr.map((options) => options.preserveWhitespaces)),\n  };\n}\n\nfunction _lastDefined<T>(args: T[]): T | undefined {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\nfunction _mergeArrays(parts: any[][]): any[] {\n  const result: any[] = [];\n  parts.forEach((part) => part && result.push(...part));\n  return result;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {ResourceLoader} from '@angular/compiler';\nimport {Injectable} from '@angular/core';\n\n@Injectable()\nexport class ResourceLoaderImpl extends ResourceLoader {\n  override get(url: string): Promise<string> {\n    let resolve: (result: any) => void;\n    let reject: (error: any) => void;\n    const promise = new Promise<string>((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function () {\n      const response = xhr.response;\n\n      let status = xhr.status;\n\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n\n    xhr.send();\n    return promise;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  COMPILER_OPTIONS,\n  CompilerFactory,\n  createPlatformFactory,\n  PlatformRef,\n  StaticProvider,\n} from '@angular/core';\nimport {platformBrowser} from '@angular/platform-browser';\nimport {ResourceLoader} from '@angular/compiler';\nimport {ResourceLoaderImpl} from './resource_loader/resource_loader_impl';\nimport {JitCompilerFactory} from './compiler_factory';\n\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS: StaticProvider[] = [\n  {\n    provide: COMPILER_OPTIONS,\n    useValue: {providers: [{provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: []}]},\n    multi: true,\n  },\n  {provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS]},\n];\n\n/**\n * @deprecated Use the `platformBrowser` function instead from `@angular/platform-browser`.\n * In case you are not in a CLI app and rely on JIT compilation, you will also need to import `@angular/compiler`\n */\nexport const platformBrowserDynamic: (extraProviders?: StaticProvider[]) => PlatformRef =\n  createPlatformFactory(\n    platformBrowser,\n    'browserDynamic',\n    INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS,\n  );\n"], "names": [], "mappings": ";;;;;;;;;;;AAQA;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACD/C,MAAM,kBAAkB,GAAqB;AAClD,IAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,IAAI,QAAQ,EAAE,EAAC;CACtD;AACD;;;;;AAKG;MACU,kBAAkB,CAAA;AACrB,IAAA,eAAe;;AAGvB,IAAA,WAAA,CAAY,cAAiC,EAAA;AAC3C,QAAA,MAAM,eAAe,GAAoB;YACvC,oBAAoB,EAAE,iBAAiB,CAAC,QAAQ;SACjD;QAED,IAAI,CAAC,eAAe,GAAG,CAAC,eAAe,EAAE,GAAG,cAAc,CAAC;;IAG7D,cAAc,CAAC,UAA6B,EAAE,EAAA;AAC5C,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/B,YAAA,SAAS,EAAE;gBACT,kBAAkB;AAClB,gBAAA;AACE,oBAAA,OAAO,EAAE,cAAc;oBACvB,UAAU,EAAE,MAAK;wBACf,OAAO,IAAI,cAAc,CAAC;4BACxB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;4BAC/C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AAC9C,yBAAA,CAAC;qBACH;AACD,oBAAA,IAAI,EAAE,EAAE;AACT,iBAAA;AACD,gBAAA,IAAI,CAAC,SAAU;AAChB,aAAA;AACF,SAAA,CAAC;AACF,QAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAEhC;AAED,SAAS,aAAa,CAAC,UAA6B,EAAA;IAClD,OAAO;AACL,QAAA,oBAAoB,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC7F,QAAA,SAAS,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAU,CAAC,CAAC;AACxE,QAAA,mBAAmB,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,mBAAmB,CAAC,CAAC;KAC5F;AACH;AAEA,SAAS,YAAY,CAAI,IAAS,EAAA;AAChC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzC,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,CAAC,CAAC;;;AAGlB,IAAA,OAAO,SAAS;AAClB;AAEA,SAAS,YAAY,CAAC,KAAc,EAAA;IAClC,MAAM,MAAM,GAAU,EAAE;AACxB,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrD,IAAA,OAAO,MAAM;AACf;;ACvEM,MAAO,kBAAmB,SAAQ,cAAc,CAAA;AAC3C,IAAA,GAAG,CAAC,GAAW,EAAA;AACtB,QAAA,IAAI,OAA8B;AAClC,QAAA,IAAI,MAA4B;QAChC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAS,CAAC,GAAG,EAAE,GAAG,KAAI;YAC/C,OAAO,GAAG,GAAG;YACb,MAAM,GAAG,GAAG;AACd,SAAC,CAAC;AACF,QAAA,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE;QAChC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC;AAC1B,QAAA,GAAG,CAAC,YAAY,GAAG,MAAM;QAEzB,GAAG,CAAC,MAAM,GAAG,YAAA;AACX,YAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ;AAE7B,YAAA,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM;;;;AAKvB,YAAA,IAAI,MAAM,KAAK,CAAC,EAAE;gBAChB,MAAM,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC;;YAG7B,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,EAAE;gBAClC,OAAO,CAAC,QAAQ,CAAC;;iBACZ;AACL,gBAAA,MAAM,CAAC,CAAA,eAAA,EAAkB,GAAG,CAAA,CAAE,CAAC;;AAEnC,SAAC;QAED,GAAG,CAAC,OAAO,GAAG,YAAA;AACZ,YAAA,MAAM,CAAC,CAAA,eAAA,EAAkB,GAAG,CAAA,CAAE,CAAC;AACjC,SAAC;QAED,GAAG,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,OAAO;;kHApCL,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAlB,kBAAkB,EAAA,CAAA;;sGAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B;;;ACUD,MAAM,2CAA2C,GAAqB;AACpE,IAAA;AACE,QAAA,OAAO,EAAE,gBAAgB;AACzB,QAAA,QAAQ,EAAE,EAAC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,EAAC;AAC1F,QAAA,KAAK,EAAE,IAAI;AACZ,KAAA;AACD,IAAA,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAC;CACnF;AAED;;;AAGG;AACI,MAAM,sBAAsB,GACjC,qBAAqB,CACnB,eAAe,EACf,gBAAgB,EAChB,2CAA2C;;;;"}